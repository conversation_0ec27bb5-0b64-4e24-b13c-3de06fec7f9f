import json
import logging
from typing import Optional
from datetime import datetime
from urllib.parse import parse_qs
from celery import shared_task
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.utils import timezone
from django.utils.timezone import localtime
from django.conf import settings
from django.forms.models import model_to_dict

# Import security utilities
from .security_utils import (
    validate_and_sanitize_message_content,
    validate_and_sanitize_postback_data,
    validate_file_url,
    validate_filename,
    SecurityValidationError
)

from connectors.line.connector_reply import reply_with_flex_v2, reply_with_text_and_images_v2, reply_with_text_v2
from connectors.line.line_config_service import LineConfigService
from connectors.line.utils import create_line_csat_score
from connectors.models import LineChannel
from connectors.services.customer_identity_service import CustomerIdentityService
from connectors.services.platform_routing_service import PlatformRoutingService
from connectors.services.platform_context import PlatformContext
from setting.services import SchedulingService, SettingsService
from user.services import UserStatusService

from customer.models import Interface
from setting.models import Conversation<PERSON>low, ChatbotProfile
from ticket.models import Ticket, Message, Status, OwnerLog, StatusLog, TicketAnalysis
from user.models import Department, User, UserRole
# from .services import LangGraphService, LineMessageService, VectorDBService
from .services import LangGraphService, VectorDBService
from ticket.utils import add_message_intents, add_ticket_intents, get_last_n_messages_formatted, is_first_agent_message, transfer_ticket_owner_v2

logger = logging.getLogger('django.chatbot_logs')

@shared_task
def test_task(x, y):
    print(f"Test task received: {x} + {y} = {x+y}")
    return x + y

# @shared_task
@shared_task(bind=True, max_retries=3, default_retry_delay=15)
def process_line_message_v2(
    self,
    channel_id,
    provider_id,
    line_user_id,
    message_content,
    message_type,
    event_reply_token,
    display_name=None,
    file_urls=None,
):
    """
    Process incoming LINE message with platform identity support and multiple image handling.
    Creates tickets at the platform identity (channel) level.
    
    Args:
        channel_id: LINE channel ID
        provider_id: Provider ID for the channel
        line_user_id: LINE user's unique ID
        message_content: Text content or placeholder for images
        message_type: Type of message (TEXT, IMAGE, FILE)
        event_reply_token: Reply token for LINE API
        display_name: User's display name
        file_urls: List of file URLs for images/files
    """
    from ticket.utils import update_ticket_status
    from setting.models import MessageTemplate
    
    platform_context = None

    closed_status = Status.objects.get(name='closed')
    system_user = User.objects.get(name='System')
    
    try:
        logger.warning(f"Processing LINE message (process_line_message_v2) from {line_user_id}")
        logger.info(f"process_line_message_v2's message_content - {message_content}")
        logger.info(f"process_line_message_v2's message_type - {message_type}")

        logger.info(f"Message type: {message_type}, File URLs count: {len(file_urls) if file_urls else 0}")
        
        # Find or create customer with platform identity        
        customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform='LINE',
            platform_user_id=line_user_id,
            provider_id=provider_id,
            channel_id=channel_id,
            display_name=display_name,
            platform_data={
                'last_message': message_content,
                'last_message_type': message_type
            }
        )
        
        # Create platform context
        platform_context = PlatformContext(platform_identity)
        if event_reply_token:
            platform_context.set_reply_token(event_reply_token)

        # Use TicketService to get or create active ticket
        from ticket.services.ticket_service import TicketService
        ticket, ticket_created = TicketService.get_or_create_active_ticket(
            platform_identity=platform_identity
        )
        
        # open_status = Status.objects.get(name="open")
        
        # # Check whether there is a non-closed ticket of this customer
        # ticket = Ticket.objects.filter(
        #     customer_id=customer,
        # ).exclude(status_id=closed_status).order_by('-created_on').first()
        
        # if not ticket:
        #     # Create new ticket
        #     interface = Interface.objects.get_or_create(
        #         name='LINE',
        #         defaults={
        #             'definition': 'LINE Messaging Interface',
        #             'created_by': system_user
        #         }
        #     )[0]
            
        #     ticket = Ticket.objects.create(
        #         customer_id=customer,
        #         status_id=open_status,
        #         owner_id=system_user,
        #         ticket_interface=interface,
        #         created_by=system_user,
        #     )
            
        #     # Create logs
        #     OwnerLog.objects.create(
        #         ticket_id=ticket,
        #         owner_id=system_user,
        #         created_by=system_user
        #     )
            
        #     StatusLog.objects.create(
        #         ticket_id=ticket,
        #         status_id=open_status,
        #         created_by=system_user
        #     )
            
        #     logger.info(f"Created new ticket {ticket.id} for customer {customer.customer_id}")
        
        # Save the incoming message with platform identity
        # SECURITY: Validate and sanitize message content before database storage
        try:
            # Sanitize message content
            sanitized_message_content = validate_and_sanitize_message_content(message_content, 'text')

            # Validate file URLs if present
            validated_file_urls = []
            if file_urls:
                for url in file_urls:
                    try:
                        validated_url = validate_file_url(url)
                        validated_file_urls.append(validated_url)
                    except SecurityValidationError as e:
                        logger.error(f"Invalid file URL detected: {url}, Error: {str(e)}")
                        # Skip invalid URLs but continue processing
                        continue

            # Sanitize display name
            sanitized_display_name = validate_and_sanitize_message_content(
                display_name or f"Customer {customer.customer_id}", 'text'
            )

        except SecurityValidationError as e:
            logger.error(f"Security validation failed for message content: {str(e)}")
            # Use safe fallback content
            sanitized_message_content = "[Content blocked for security reasons]"
            validated_file_urls = []
            sanitized_display_name = f"Customer {customer.customer_id}"

        # Check message_type and proceed to save incoming message accordingly
        if message_type == Message.MessageType.IMAGE and validated_file_urls:
            # For image messages with multiple URLs, create a single message
            customer_message = Message.objects.create(
                ticket_id=ticket,
                platform_identity=platform_identity,
                message=sanitized_message_content,
                user_name=sanitized_display_name,
                is_self=False,  # Message from customer
                message_type=message_type,
                status=Message.MessageStatus.DELIVERED,
                file_url=validated_file_urls,  # Store validated URLs
                metadata={
                    'platform': 'LINE',
                    'platform_user_id': line_user_id,
                    'channel_id': channel_id,
                    'provider_id': provider_id,
                    'image_count': len(validated_file_urls),
                    'reply_token': event_reply_token,
                    'security_validated': True  # Mark as security validated
                },
                created_by=User.objects.get(name='System')
            )
            
            logger.info(f"Created message {customer_message.id} (Message's type {message_type}) with {len(file_urls)} image URLs")

        else:
            # Save text message (content already sanitized above)
            customer_message = Message.objects.create(
                ticket_id=ticket,
                message=sanitized_message_content,
                user_name=sanitized_display_name,
                is_self=False,
                message_type=message_type,
                status=Message.MessageStatus.DELIVERED,
                platform_identity=platform_identity,
                metadata={
                    'line_channel_id': channel_id,
                    'provider_id': provider_id,
                    'reply_token': event_reply_token,
                    'security_validated': True  # Mark as security validated
                }
            )

            logger.info(f"Created message {customer_message.id} (Message's type {message_type})")
        
        # Update platform identity last interaction
        platform_identity.last_interaction = timezone.now()
        platform_identity.save(update_fields=['last_interaction'])
        
        # # Broadcast to WebSocket
        # from ticket.tasks import broadcast_to_websocket
        # broadcast_to_websocket.delay(
        #     ticket_id=ticket.id,
        #     message_id=customer_message.id,
        #     action='new_customer_message'
        # )

        # Broadcast incoming message to WebSocket
        from customer.tasks import broadcast_platform_message_update
        if platform_identity:
            # TODO - Delete this
            logger.info(f"process_line_message_v2's broadcast_platform_message_update - {platform_identity.id} - {customer_message.id}")
            broadcast_platform_message_update.delay(
                platform_identity_id=platform_identity.id,
                message_id=customer_message.id
            )
        
        # TODO - Check this
        # This section of codes, make circular dependencies import problem
        # Checked ticket status for CSAT, in case a customer decide to typing anything instead of selecting an quick-reply options
        pending_to_close_status = Status.objects.get(name='pending_to_close')
        if ticket.status_id == pending_to_close_status:       
            prompt_message = f"""ขอบคุณที่ให้คะแนน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n\nThank you for giving us your feedback. If you would like to inquire any information, you can contact us 24 hours a day. """
            send_message_via_route_message_to_customer.delay(
                    ticket_id=ticket.id,
                    message_content=prompt_message,
                    message_type='TEXT',
                    # metadata={
                    #     # 'line': {
                    #     #     'image_map': line_csat_score_imagemap.to_dict(),
                    #     # }
                    #     'image_map': line_csat_score_imagemap
                    # },
                    event_reply_token=event_reply_token,
                    bool_create_outgoing_message=True
                )
            update_ticket_status(ticket=ticket, new_status=closed_status, user=ticket.owner_id)
            
            # # Send CSAT survey
            # reply_csat_score_v2(
            #     line_configuration=platform_context.channel_config,
            #     line_user_id=line_user_id,
            #     ticket_id=ticket.id,
            #     event_reply_token=event_reply_token if platform_context.should_use_reply() else None
            # )
            
            return {
                'status': 'success',
                'action': 'csat_sent',
                'ticket_id': ticket.id
            }
        
        # Check if ticket owner is System (chatbot should respond)
        owner_role = UserRole.objects.filter(user_id=ticket.owner_id).first()

        # Set default values
        message_template = None
        message_template_id = None 
        ticket_llm_endpoint = "default"
        agent = False
        recommended_products = None

        text            = None
        quick_reply     = None
        image           = None
        image_map       = None
        image_carousel  = None
        carousel        = None
        buttons_template= None
        confirm_template= None
        message_template_status = None
        message_template_department_list_of_ids = None

        print('process_line_message_v2 - platform:', platform_identity)
        print('process_line_message_v2 - customer:', customer)
        
        channel_field_mapping = {
            'line': 'line_channel__name',
            'facebook': 'facebook_channel__name', 
            'whatsapp': 'whatsapp_channel__name'
        }
        
        logger.info('process_line_message_v2 - platform_identity.channel_name:', getattr(platform_identity, 'channel_name', None))
        logger.info('process_line_message_v2 - platform_identity.id:', getattr(platform_identity, 'id', None))

        filter_kwargs = {'social_app': platform_identity.platform.lower()}

        if platform_identity.platform in channel_field_mapping and platform_identity.channel_name:
            channel_field = channel_field_mapping[platform_identity.platform]
            filter_kwargs[channel_field] = platform_identity.channel_name

        conversation_flow = ConversationFlow.objects.filter(**filter_kwargs).first()
        chatbot_profile = ChatbotProfile.objects.get(chatbot_id=conversation_flow.chatbot_id)
            
        # Prepare user profile with platform context
        user_profile = {
            "channel"           : "all",  # Keep for backward compatibility
            "name"              : platform_context.get_display_name(),
            "social_platform"   : platform_identity.platform,
            # load channel name from platform identity
            "line_channel"      : getattr(platform_identity, 'channel_name', None),
            "facebook_channel"  : None,
            "whatsapp_channel"  : None,
            # "conversation_flow"  : model_to_dict(conversation_flow),
            "chatbot_profile"    : model_to_dict(chatbot_profile)
            # "platform": platform_identity.platform,
            # "platform_id": platform_identity.id,
            # "customer_id": customer.customer_id
        }    
        
        logger.info(f"process_line_message_v2 - user_profile for LLM: {user_profile}")
        
        if message_type == Message.MessageType.IMAGE:
            llm_response = {}
            if file_urls and len(file_urls) > 1:
                llm_response["result"] = f"ได้รับรูปภาพ {len(file_urls)} รูปเรียบร้อยแล้ว 📷 ขอบคุณที่ส่งมา"
            else:
                llm_response["result"] = "ได้รับรูปภาพเรียบร้อยแล้ว 📷 ขอบคุณที่ส่งมา"

            outgoing_message = llm_response.get('result', None)

            outgoing_message_type = Message.MessageType.TEXT
            
            

        else:
            # Get chat history and process with LLM
            chat_history = get_last_n_messages_formatted(platform_identity)

            logger.info(f"process_line_message_v2's llm_service's message_type.lower() - {message_type.lower()}")
            # Call LLM service
            llm_service = LangGraphService(base_url=settings.LANGGRAPH_BASE_URL)
        
            llm_response = llm_service.get_response(
                question            = message_content,
                chat_history        = chat_history,
                # postback_event    = {
                #     "message_type" : message_type.lower(),
                #     "data" : message_content
                # },
                # message_type=message_type.lower(),
                user_profile        = user_profile,
                ticket_llm_endpoint = ticket.llm_endpoint,
                thread_id           = customer.customer_id,
                ticket_id           = ticket.id
            )
        
            # llm_response = {'ticket_id': '26', 'status': 'default', 'message_type': 'text', 'result': 'Hello! Salmate is here again. How can I assist you today?', 'intention': 'General', 'sub_intent': 'Chitchat', 'language': 'English', 'chat_history': 'human:Hello at 20.41\nchatbot:Hello! Salmate is here again. How can I assist you today?\nhuman:มีประกันไรมั้ง\nchatbot:เรามีผลิตภัณฑ์ประกันภัยที่หลากหลาย โดยเฉพาะประกันรถยนต์ 🚗 หากคุณลูกค้าสนใจรายละเอียดเพิ่มเติมเกี่ยวกับประกันรถยนต์ สามารถสอบถามได้เลย 📞\nhuman:Hello at 20.48\nchatbot:Hello! Salmate is here again. How can I assist you today?\nhuman:Hello at 21.00\nchatbot:Hello! Salmate is here again. How can I assist you today?\nhuman:Hello at 21.05\nhuman:Hello at 21.08', 'question': 'Hello at 21.08', 'state': {'text': '0', 'emotion': 'neutral', 'explanation': 'The user is simply greeting the chatbot again without any specific request or emotion expressed, indicating a neutral tone.', 'label': 'General', 'sub_intent': 'Chitchat', 'usage': {'total_cost': 0.0004506, 'total_tokens': 2800, 'prompt_tokens': 2732, 'prompt_tokens_cached': 0, 'completion_tokens': 68, 'reasoning_tokens': 0, 'successful_requests': 1}}, 'message_event': {}, 'user_name': '★ Boss.SCh™', 'channel': 'all', 'agent': False, 'previous_product': [], 'product': [], 'source': None, 'analysis': {'analysis_product': {'reasoning': 'User is inquiring about available insurance products, indicating a desire to know what types of insurance are offered. This falls under the Product Search category as the user is looking for specific product information. The chat history shows no explicit mention of promotions or customer support needs, focusing solely on product availability.', 'topic': ['Product Search'], 'language': 'Thai', 'query_database': {'Product Search': 'มีประกันประเภทไหนบ้างที่คุณมีให้บริการ?'}, 'status': 'default', 'categories': ['NONE'], 'car_type': ['NONE'], 'level': ['NONE'], 'health_type': ['NONE'], 'home_type': ['NONE'], 'business_type': ['NONE'], 'business_group': ['NONE'], 'shipping_type': ['NONE'], 'sum_insured': 'NONE', 'age': 'NONE', 'liquidity': 'NONE', 'brochure_intent': 'yes'}, 'analysis_customer_support': None, 'analysis_promotion': None}, 'usage': [{'total_cost': 0.0007143, 'total_tokens': 4390, 'completion_tokens': 124, 'prompt_tokens_cached': 0, 'reasoning_tokens': 0, 'successful_requests': 2, 'prompt_tokens': 4266}], 'quick_reply': [], 'last_node': 'default_node', 'message_template': {'text': 'Hello! Salmate is here again. How can I assist you today?', 'quick_reply': [], 'image_map': {}, 'carousel': {}, 'image_carousel': {}}, 'required_human': {'claim': False, 'endorsement': False, 'quotation': False, 'reprint': False, 'renewal_notice': False, 'cancel': False}}

            # TODO - Agree on which key will be used to represent word to show on Chat center when it send LINE element without values in result or text key
            # Extract response data
            if llm_response.get('result', None):
                outgoing_message = llm_response.get('result', None)
            elif  llm_response.get('message_template', None).get('text', None):
                outgoing_message = llm_response.get('message_template').get('text', None)
            else:
                outgoing_message = ""

            # Process LLM response metadata

            state               = llm_response.get('state', {})           
            message_intent      = state.get('label') if state else None
            sub_message_intent  = state.get('sub_intent') if state else None
            company_code        = llm_response.get('channel')
            agent               = llm_response.get('agent')
            ticket_llm_endpoint = llm_response.get('status', None)
            recommended_products = llm_response.get('product', None)
                            
            message_type_response = llm_response.get('message_template', {}).get('message_type', {})
            message_template_id = llm_response.get('message_template', {}).get('id', None)

            # LINE element
            if not (message_type_response):
                text            = None
                quick_reply     = llm_response.get('quick_reply', [])
                image           = None
                image_map       = None
                image_carousel  = None
                carousel        = None
                buttons_template= None
                confirm_template= None

            else:
                text            = message_type_response.get('text')
                quick_reply     = message_type_response.get('quick_reply')
                image_map       = message_type_response.get('image_map')
                image           = message_type_response.get('image')
                image_carousel  = message_type_response.get('image_carousel')
                carousel        = message_type_response.get('carousel')
                buttons_template= message_type_response.get('buttons_template')
                confirm_template= message_type_response.get('confirm_template')

            if message_template_id:
                try:
                    message_template = MessageTemplate.objects.get(id=message_template_id)
                    logger.info(f"process_line_message_v2's Found message template: {message_template}")

                    message_template_status = message_template.status
                    message_template_department_list_of_ids = message_template.department

                    ticket_llm_endpoint = message_template_status

                except MessageTemplate.DoesNotExist:
                    message_template = None
                # TODO - 
                outgoing_message_type = Message.MessageType.ALTERNATIVE
            else:
                message_template = None
                outgoing_message_type = Message.MessageType.TEXT
            
            # Update ticket and message intents
            if message_intent:
                ticket = add_ticket_intents(ticket_id=ticket.id, new_message_intent=message_intent)
                customer_message = add_message_intents(
                    message=customer_message,
                    new_message_intent=message_intent,
                    new_sub_message_intent=sub_message_intent
                )
                # customer_message.llm_endpoint = ticket_llm_endpoint
                customer_message.save()

        # Update ticket endpoint
        if ticket_llm_endpoint == None:
            ticket_llm_endpoint = 'default'
        else:
            ticket_llm_endpoint = ticket_llm_endpoint
        logger.info(f"process_line_message_v2's ticket_llm_endpoint - {ticket_llm_endpoint}")
        ticket.llm_endpoint = ticket_llm_endpoint
        ticket.save()
            
        # TODO - Deltet this or Log this
        logger.info(f"process_line_message_v2's Found message template's status: {message_template_status}")
        logger.info(f"process_line_message_v2's Found message template's department: {message_template_department_list_of_ids}")
        logger.info(f"process_line_message_v2's message_type - {message_type}")
        logger.info(f"process_line_message_v2's file_urls - {file_urls}")
        logger.info(f"process_line_message_v2's llm_response - {llm_response}")
        logger.info(f"process_line_message_v2 - message_template_id: {message_template_id}")
        logger.info(f"process_line_message_v2 - message_template: {message_template}")
        
        

        if owner_role and owner_role.role_id.name == "System":
            try:
                # Add ticket ID for first message
                # if is_first_agent_message(ticket.id):
                #     icon_ticket = "\U0001F3AB"
                #     outgoing_message = f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n" + outgoing_message

                # Create outgoing message
                outgoing_message_obj = Message.objects.create(
                    ticket_id=ticket,
                    message=outgoing_message,
                    user_name=ticket.owner_id.name,
                    is_self=True,
                    message_type=outgoing_message_type,
                    status=Message.MessageStatus.SENDING,
                    platform_identity=platform_identity, # Send to this customer's platform_identity
                    message_template=message_template,
                    metadata={
                        'line_channel_id': channel_id,
                        'provider_id': provider_id,
                        'reply_token': event_reply_token
                    },
                    created_by=system_user
                )
                
                # print('process_line_message_v2 - platform_identity:', platform_identity.id)
                logger.info(f"process_line_message_v2 - outgoing_message_obj: {outgoing_message_obj.id} - {outgoing_message_type} - {message_template_id}")

                
                # Send response through platform routing
                result = PlatformRoutingService.route_message_to_customer(
                    customer        = customer,
                    message_content = outgoing_message,
                    message_type    = 'TEXT', # TODO - Update this (outgoing_message_type)
                    preferred_platform_identity_id=platform_identity.id,
                    metadata={
                        'ticket_id'         : ticket.id,
                        'message_id'        : outgoing_message_obj.id,
                        'message_template_id': message_template_id,
                        'text'              : text,
                        'quick_reply'       : quick_reply,
                        'image'             : image,
                        'image_map'         : image_map,
                        'image_carousel'    : image_carousel,
                        'carousel'          : carousel,
                        'buttons_template'  : buttons_template,
                        'confirm_template'  : confirm_template,
                        'recommended_products': recommended_products,
                        # 'reply_token': event_reply_token if platform_context.should_use_reply() else None
                        'reply_token': event_reply_token if event_reply_token else None
                    }
                )
                print(f"process_line_message_v2 - result: {result}")
                
                if result['success']:
                    # Update message status
                    outgoing_message_obj.status = Message.MessageStatus.DELIVERED
                    outgoing_message_obj.delivered_on = timezone.now()
                    outgoing_message_obj.save()
                else:
                    # Update to failed
                    outgoing_message_obj.status = Message.MessageStatus.FAILED
                    outgoing_message_obj.save()
                
                # # Broadcast outgoing message to WebSocket
                # broadcast_to_websocket.delay(
                #     ticket_id=ticket.id,
                #     message_id=outgoing_message_obj.id,
                #     action='update_line_message'
                # )

                # Broadcast outgoing message to WebSocket
                from customer.tasks import broadcast_platform_message_update
                if platform_identity:
                    # TODO - Delete this
                    logger.info(f"process_line_message_v2's broadcast_platform_message_update - {platform_identity.id} - {outgoing_message_obj.id}")
                    broadcast_platform_message_update.delay(
                        platform_identity_id=platform_identity.id,
                        message_id=outgoing_message_obj.id
                    )

                if message_template_status == "close" and ticket.status_id.name == "open":
                    update_ticket_status(ticket=ticket, new_status=closed_status, user=ticket.owner_id)

                # Handle agent transfer if needed
                # if agent or (ticket_llm_endpoint == 'transfered'):
                elif agent or message_template_status == "transferred":
                    transfer_to_department_codes=None
                    transfer_to_user_tags=[]
                    # Extract prefer user's department if provided
                    if message_template_department_list_of_ids:
                            departments = Department.objects.filter(id__in=message_template_department_list_of_ids)
                            # TODO - If automate transfering can selecta suitable user from one of department codes then this section of codes have to be updated
                            transfer_to_department_codes = list(departments.values_list('code', flat=True))[0]

                    else:
                        # Get transfer context from Vector DB
                        vector_db_api = VectorDBService(base_url=settings.VECTORDB_API_URL)
                        vector_db_response = vector_db_api.get_response(
                            endpoint="tools_transfering",
                            user_profile={
                                'user_id': ticket.customer_id.customer_id,
                                'user_name': platform_context.get_display_name(),
                                "social_platform": "line"
                            },
                            previous_messages=chat_history
                        )
                        transfer_to_department_codes=vector_db_response.get('department', None)
                        transfer_to_user_tags=vector_db_response.get('tags', [])

                    logger.info(f"process_line_message_v2's transfer_to_department_codes - {transfer_to_department_codes}")
                    logger.info(f"process_line_message_v2's transfer_to_user_tags - {transfer_to_user_tags}")
                    
                    handle_agent_transfer_v2.delay(
                        ticket_id=ticket.id,
                        ticket_interface_name=ticket.ticket_interface.name,
                        # company_code=company_code,
                        department_code=transfer_to_department_codes,
                        user_tags=transfer_to_user_tags,
                        platform_context={
                            'platform': platform_identity.platform,
                            'platform_identity_id': platform_identity.id,
                            'channel_id': channel_id
                        }
                    )
                
            except Exception as llm_error:
                logger.error(f"Error outgoging message processing for LINE's MESSAGE event: {str(llm_error)}")
                
                # Send error message back to user
                error_message = "ขออภัย ระบบขัดข้อง กรุณาลองใหม่อีกครั้ง\n(Sorry, I encountered an issue. Please try again later.)"
                
                # TODO - Open this after implement everything in try section
                # # Create error message in database
                # error_message_obj = Message.objects.create(
                #     ticket_id=ticket,
                #     message=error_message,
                #     user_name="System",
                #     is_self=True,
                #     message_type=Message.MessageType.TEXT,
                #     status=Message.MessageStatus.SENDING,
                #     created_by=system_user
                # )
                
                # # Send error through platform routing
                # error_result = PlatformRoutingService.route_message_to_customer(
                #     customer=customer,
                #     message_content=error_message,
                #     message_type='TEXT',
                #     preferred_platform_identity_id=platform_identity.id,
                #     metadata={
                #         'ticket_id': ticket.id,
                #         'message_id': error_message_obj.id,
                #         'is_error': True,
                #         'reply_token': event_reply_token if platform_context.should_use_reply() else None
                #     }
                # )
                
                # if error_result['success']:
                #     error_message_obj.status = Message.MessageStatus.DELIVERED
                #     error_message_obj.delivered_on = timezone.now()
                # else:
                #     error_message_obj.status = Message.MessageStatus.FAILED
                
                # error_message_obj.save()
                
                # # Broadcast error message
                # broadcast_to_websocket.delay(
                #     ticket_id=ticket.id,
                #     message_id=error_message_obj.id,
                #     action='update_line_message'
                # )
                
                # # Transfer to agent if ticket is still with System
                # if ticket.status_id == open_status:
                #     handle_agent_transfer_v2.delay(
                #         ticket_id=ticket.id,
                #         ticket_interface_name=ticket.ticket_interface.name,
                #         company_code=None,
                #         platform_context={
                #             'platform': platform_identity.platform,
                #             'platform_identity_id': platform_identity.id,
                #             'channel_id': channel_id
                #         }
                #     )
        
        else:
            if message_template:
                # Create outgoing message
                outgoing_message_obj = Message.objects.create(
                    ticket_id=ticket,
                    message=outgoing_message,
                    user_name=ticket.owner_id.name,
                    is_self=True,
                    message_type=outgoing_message_type,
                    status=Message.MessageStatus.SENDING,
                    platform_identity=platform_identity, # Send to this customer's platform_identity
                    message_template=message_template,
                    metadata={
                        'line_channel_id': channel_id,
                        'provider_id': provider_id,
                        'reply_token': event_reply_token
                    },
                    created_by=system_user
                )
                
                # print('process_line_message_v2 - platform_identity:', platform_identity.id)
                logger.info(f"process_line_message_v2 - outgoing_message_obj: {outgoing_message_obj.id} - {outgoing_message_type} - {message_template_id}")

                
                # Send response through platform routing
                result = PlatformRoutingService.route_message_to_customer(
                    customer        = customer,
                    message_content = outgoing_message,
                    message_type    = 'TEXT', # TODO - Update this (outgoing_message_type)
                    preferred_platform_identity_id=platform_identity.id,
                    metadata={
                        'ticket_id'         : ticket.id,
                        'message_id'        : outgoing_message_obj.id,
                        'message_template_id': message_template_id,
                        'text'              : text,
                        'quick_reply'       : quick_reply,
                        'image'             : image,
                        'image_map'         : image_map,
                        'image_carousel'    : image_carousel,
                        'carousel'          : carousel,
                        'buttons_template'  : buttons_template,
                        'confirm_template'  : confirm_template,
                        'recommended_products': recommended_products,
                        # 'reply_token': event_reply_token if platform_context.should_use_reply() else None
                        'reply_token': event_reply_token if event_reply_token else None
                    }
                )
                print(f"process_line_message_v2 - result: {result}")
                
                if result['success']:
                    # Update message status
                    outgoing_message_obj.status = Message.MessageStatus.DELIVERED
                    outgoing_message_obj.delivered_on = timezone.now()
                    outgoing_message_obj.save()
                else:
                    # Update to failed
                    outgoing_message_obj.status = Message.MessageStatus.FAILED
                    outgoing_message_obj.save()
                
                # # Broadcast outgoing message to WebSocket
                # broadcast_to_websocket.delay(
                #     ticket_id=ticket.id,
                #     message_id=outgoing_message_obj.id,
                #     action='update_line_message'
                # )

                # Broadcast outgoing message to WebSocket
                from customer.tasks import broadcast_platform_message_update
                if platform_identity:
                    # TODO - Delete this
                    logger.info(f"process_line_message_v2's broadcast_platform_message_update - {platform_identity.id} - {outgoing_message_obj.id}")
                    broadcast_platform_message_update.delay(
                        platform_identity_id=platform_identity.id,
                        message_id=outgoing_message_obj.id
                    )

            # else:
            #     # Ticket is with human agent - just notify them
            #     if ticket.owner_id and ticket.owner_id.name != 'System':
            #         from ticket.tasks import notify_new_customer_message
            #         notify_new_customer_message.delay(customer_message.id)
        
        return {
            'status': 'success',
            'ticket_id': ticket.id,
            'message_id': customer_message.id,
            'platform': platform_identity.platform,
            'customer_id': customer.customer_id
        }
        
    except Exception as e:
        logger.error(f"Error processing LINE message: {str(e)}")
        
        # Try to send error message if we have platform context
        if platform_context and platform_identity:
            try:
                error_msg = "ขออภัย เกิดข้อผิดพลาดในระบบ\n(System error occurred. Please try again.)"
                error_msg = "ขออภัย เกิดข้อผิดพลาดในระบบ"
                PlatformRoutingService.route_message_to_customer(
                    customer=platform_identity.customer,
                    message_content=error_msg,
                    message_type='TEXT',
                    preferred_platform_identity_id=platform_identity.id,
                    metadata={
                        'is_error': True,
                        'reply_token': event_reply_token if platform_context.should_use_reply() else None
                    }
                )
            except:
                pass
        
        return {
            'status': 'error',
            'error': str(e)
        }

# # TODO - Check this and its newer version and Delete this if no longer needed
# # This 2nd version is work before new message status updates and batch processing of Chat Center page
# @shared_task(bind=True, max_retries=3, default_retry_delay=30)
# def process_line_postback_v2(
#     self,
#     channel_id: str,
#     provider_id: str,
#     line_user_id: str,
#     postback_data: str,
#     event_reply_token: str = None,
#     display_name: str = None
# ):
#     """
#     Process LINE postback events with support for LINE elements
#     """
#     try:
#         logger.warning(f"Processing LINE postback (process_line_postback_v2) from {line_user_id}")
#         logger.info(f"process_line_postback_v2's postback_data - {postback_data}")

#         # TODO - Agree on what will be message_content and message_type
#         message_content = f"[Postback Data: {postback_data}]"
#         message_type = 'TEXT'  # Assuming postback messages are treated as such
        
#         # Find or create customer with platform identity
#         from connectors.services.customer_identity_service import CustomerIdentityService
#         from connectors.services.platform_context import PlatformContext
        
#         customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
#             platform='LINE',
#             platform_user_id=line_user_id,
#             provider_id=provider_id,
#             channel_id=channel_id,
#             display_name=display_name,
#             platform_data={
#                 'last_message': message_content,
#                 'last_message_type': message_type
#             }
#         )

#         # Create platform context
#         platform_context = PlatformContext(platform_identity)
#         if event_reply_token:
#             platform_context.set_reply_token(event_reply_token)

#         # Get or create ticket
#         # active_ticket = Ticket.objects.filter(
#         #     customer=customer,
#         #     status__name__in=['open', 'pending', 'assigned']
#         # ).order_by('-created_on').first()








#         # open_status = Status.objects.get(name="open")
#         # closed_status = Status.objects.get(name="closed")
#         # system_user = User.objects.get(name='System')
#         # # Check whether there is a non-closed ticket of this customer
#         # # If there is, get the latest one
#         # active_ticket = Ticket.objects.filter(
#         #     customer_id=customer,
#         # ).exclude(status_id=closed_status).order_by('-created_on').first()


#         # print(f"process_line_postback_v2 - active Ticket : {active_ticket}")

#         # #Ticket status might be close, transfer    
#         # if not active_ticket: 
#         #     print("active_ticket is True")
#         #     # Create new ticket if needed
#         #     # active_ticket = Ticket.objects.create(
#         #     #     customer=customer,
#         #     #     subject=f"Postback: {action}",
#         #     #     status=Status.objects.get(name='open'),
#         #     #     created_by=User.objects.get(name='System')
#         #     # )

#         #     interface = Interface.objects.get_or_create(
#         #         name='LINE',
#         #         defaults={
#         #             'definition': 'LINE Messaging Interface',
#         #             'created_by': system_user
#         #         }
#         #     )[0]

#         #     active_ticket = Ticket.objects.create(
#         #         customer_id=customer,
#         #         status_id=open_status,
#         #         owner_id=system_user,
#         #         ticket_interface=interface,
#         #         created_by=system_user,
#         #     )

#         #     # Create logs
#         #     OwnerLog.objects.create(
#         #         ticket_id=active_ticket,
#         #         owner_id=system_user,
#         #         created_by=system_user
#         #     )
            
#         #     StatusLog.objects.create(
#         #         ticket_id=active_ticket,
#         #         status_id=open_status,
#         #         created_by=system_user
#         #     )
            
#         #     logger.info(f"Created new ticket {active_ticket.id} for customer {customer.customer_id}")








#         system_user = User.objects.get(name='System')
        
#         from ticket.services.ticket_service import TicketService
#         active_ticket, ticket_created = TicketService.get_or_create_active_ticket(
#             platform_identity=platform_identity
#         )


#         # owner_role = UserRole.objects.filter(user_id=active_ticket.owner_id).first()
#         # if owner_role and owner_role.role_id.name == "System":

#         # Parse postback data
#         parse_action    = parse_qs(postback_data)
#         action_name     = (parse_action.get('action') or [None])[0]
#         variable_name   = (parse_action.get('variable') or [None])[0]
#         value_name      = (parse_action.get('value') or [None])[0]
#         status_name     = (parse_action.get('status') or [None])[0]
#         print(f"process_line_postback_v2 - parse_action: {parse_action}")

#         # Parse postback data
#         # params = parse_qs(postback_data)
#         # postback_action = params.get('action', [None])[0]
#         postback_label = ""
#         # postback_label = postback_action.strip().replace("_", " ")
        
#         # TODO - Add conditions here so some Postback's action do not have to go through LLM
        
#         # from ticket.models import MessageTemplate
#         # try:
#         #     message_template = MessageTemplate.objects.filter(label=postback_action.strip().replace("_", " ")).first()
#         #     # message_content = message_template.sentence  # Use template content as message
#         #     # message_content = message_template.label  # Use template content as message
#         #     postback_label = message_template.label
#         #     # logger.info(f"Using MessageTemplate for action '{postback_action}': {message_content} with label '{postback_label}'")
#         #     logger.info(f"Using MessageTemplate for action '{postback_action}' with label '{postback_label}'")
#         #     print(f"Using MessageTemplate for action '{postback_action}' with label '{postback_label}'")

#         # except MessageTemplate.DoesNotExist:
#         #     logger.warning(f"MessageTemplate with Action {postback_action} not found, using default content")
#         #     print(f"MessageTemplate with Action {postback_action} not found, using default content")

#         # if postback_action in ['CSAT_SCORE_SUBMIT', 'IMMEDIATE_ACTION']:
#         print(f"process_line_postback_v2 - postback_label : {postback_label}")
#         if variable_name.lower() in ["csat_rating"]:
#             # csat_rating = int(params.get('rating', ['0'])[0])
#             # incoming_message = params.get('text', [''])[0]
#             # # ticket_id = params.get('ticket_id', [''])[0]
#             # logger.info(f"Received CSAT rating {csat_rating} for Ticket ID: {active_ticket.id}")
            

#             postback_variable = parse_action.get('variable', ['0'])[0]
#             postback_value = parse_action.get('value', [''])[0]
#             # ticket_id = parse_action.get('ticket_id', [''])[0]
#             logger.info(f"Received Action: {action_name} with variable {postback_variable} and value '{postback_value}' for Ticket ID: {active_ticket.id}")
            

#             # Save the incoming message with platform identity
#             message_content = f"CSAT Score: {postback_value}"
#             customer_message = Message.objects.create(
#                 ticket_id=active_ticket,
#                 message=message_content,
#                 user_name=platform_context.get_display_name(),
#                 is_self=False,
#                 message_type=Message.MessageType.ALTERNATIVE,
#                 status=Message.MessageStatus.DELIVERED,
#                 platform_identity=platform_identity,
#                 metadata={
#                     'line_channel_id': channel_id,
#                     'provider_id': provider_id,
#                     'reply_token': event_reply_token
#                 }
#             )
            
#             # Update platform identity last interaction
#             platform_identity.last_interaction = timezone.now()
#             platform_identity.save(update_fields=['last_interaction'])
            
#             # Broadcast to WebSocket
#             from ticket.tasks import broadcast_to_websocket
#             broadcast_to_websocket.delay(
#                 ticket_id=active_ticket.id,
#                 message_id=customer_message.id,
#                 action='new_customer_message'
#             )

            
#             # Save CSAT rating
#             active_ticket.feedback = {'csat': f"{postback_value}"}
#             active_ticket.save()


#             logger.info(f"Customer {customer.customer_id} gave CSAT score {postback_value} "
#                     f"for Ticket {active_ticket.id}")

#             outgoing_message = f"""ขอบคุณที่ให้คะแนน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\nThank you for giving us your feedback. If you would like to inquire any information, you can contact us 24 hours a day. """
#             line_elements = {
#                 'quick_reply': None,
#                 'image_map': None,
#                 'image_carousel': None,
#                 'carousel': None,
#             }

#             # Create outgoing message
#             outgoing_message_obj = Message.objects.create(
#                 ticket_id=active_ticket,
#                 message=outgoing_message,
#                 user_name=active_ticket.owner_id.name,
#                 is_self=True,
#                 # message_type=Message.MessageType.TEXT,
#                 message_type=Message.MessageType.ALTERNATIVE,
#                 status=Message.MessageStatus.SENDING,
#                 platform_identity=platform_identity, # Send to this customer's platform_identity
#                 metadata={
#                     'line_channel_id': channel_id,
#                     'provider_id': provider_id,
#                     'reply_token': event_reply_token
#                 },
#                 created_by=system_user
#             )
            
#             # Route message with LINE elements
#             result = PlatformRoutingService.route_message_to_customer(
#                 customer=customer,
#                 message_content=outgoing_message,
#                 message_type='TEXT', # TODO - Update this (outgoing_message_type)
#                 # message_type=Message.MessageType.ALTERNATIVE, # TODO - Update this (outgoing_message_type)
#                 preferred_platform_identity_id=platform_identity.id,
#                 metadata={
#                     'ticket_id': active_ticket.id,
#                     'message_id': outgoing_message_obj.id,
#                     'quick_reply': line_elements.get('quick_reply'),
#                     'image_map': line_elements.get('image_map'),
#                     'image_carousel': line_elements.get('image_carousel'),
#                     'carousel': line_elements.get('carousel'),
#                     'reply_token': event_reply_token
#                 }
#             )
            
#             # logger.info(f"Response to {postback_action} action sent successfully")


#             # # TODO - Check this
#             # # TODO - Update Ticket Status
#             # Change Ticket Status from 'pending_to_close' to 'closed'
#             closed_status = Status.objects.get(name='closed')
#             pending_to_close_status = Status.objects.get(name='pending_to_close')
#             ticket_owner = active_ticket.owner_id
#             if active_ticket.status_id == pending_to_close_status:
#                 from ticket.utils import update_ticket_status
#                 update_ticket_status(ticket=active_ticket, new_status=closed_status,user=ticket_owner)


#             # else:
#             #     chat_history = get_last_n_messages_formatted(customer)

#             #     # Convert postback to a message format for LLM
#             #     postback_message = f"[Postback Action: {postback_action}]"
#             #     if params:
#             #         postback_message += f" Parameters: {json.dumps(params)}"
                
#             #     # # Get LLM response with potential LINE elements
#             #     # llm_response = llm_service.get_response(
#             #     #     conversation_history=[],
#             #     #     current_message=postback_message,
#             #     #     user_session=get_session(line_user_id),
#             #     #     metadata={
#             #     #         'postback': True,
#             #     #         'action': action,
#             #     #         'params': params
#             #     #     }
#             #     # )

#             #     # Prepare user profile with platform context
#             #     user_profile = [{
#             #         "channel": "all",  # Keep for backward compatibility
#             #         "name": platform_context.get_display_name(),
#             #         "social_platform": "line"
#             #     }]

#             #     # Process the postback action
#             #     llm_service = LangGraphService()
#             #     llm_response = llm_service.get_response(
#             #         question=message_content,
#             #         chat_history=chat_history,
#             #         message_type=message_type.lower(),
#             #         user_profile=user_profile,
#             #         ticket_llm_endpoint=active_ticket.llm_endpoint,
#             #         thread_id=customer.customer_id,
#             #         ticket_id=active_ticket.id
#             #     )
                
#             #     # Extract response and LINE elements
#             #     if llm_response.get('result', None):
#             #         outgoing_message = llm_response.get('result', None)
#             #     elif  llm_response.get('message_template').get('text', None):
#             #         outgoing_message = llm_response.get('message_template').get('text', None)
#             #     else:
#             #         outgoing_message = ""
#             #     line_elements = {
#             #         'quick_reply': llm_response.get('message_template').get('quick_reply', None),
#             #         'image_map': llm_response.get('message_template').get('image_map', None),
#             #         'image_carousel': llm_response.get('message_template').get('image_carousel', None),
#             #         'carousel': llm_response.get('message_template').get('carousel', None),
#             #     }

#             #     ticket_llm_endpoint = llm_response.get('status')
#             #     # Update ticket endpoint
#             #     active_ticket.llm_endpoint = ticket_llm_endpoint
#             #     active_ticket.save()
            
#             #     # Create outgoing message
#             #     outgoing_message_obj = Message.objects.create(
#             #         ticket_id=active_ticket,
#             #         sender=User.objects.get(name='System'),
#             #         receiver=customer.user,
#             #         content=outgoing_message,
#             #         type=Message.MessageType.TEXT,
#             #         is_self=True,
#             #         status=Message.MessageStatus.PENDING
#             #     )
                
#             #     # Route message with LINE elements
#             #     result = PlatformRoutingService.route_message_to_customer(
#             #         customer=customer,
#             #         message_content=outgoing_message,
#             #         message_type='TEXT',
#             #         preferred_platform_identity_id=platform_identity.id,
#             #         metadata={
#             #             'ticket_id': active_ticket.id,
#             #             'message_id': outgoing_message_obj.id,
#             #             'quick_reply': line_elements.get('quick_reply'),
#             #             'image_map': line_elements.get('image_map'),
#             #             'image_carousel': line_elements.get('image_carousel'),
#             #             'carousel': line_elements.get('carousel'),
#             #             'reply_token': event_reply_token
#             #         }
#             #     )

            
#             ticket_llm_endpoint = "default"
#             agent = False
            
#             logger.info(f"Postback response sent successfully")

#         else:
#             print("process_line_postback_v2 - call llm")
#             #Ticket status is open
#             chat_history = get_last_n_messages_formatted(platform_identity)
#             # Prepare user profile with platform context
#             user_profile = {
#                 "channel": "all",  # Keep for backward compatibility
#                 "name": platform_context.get_display_name(),
#                 "social_platform" : platform_identity.platform,
#             }

#             # Process the postback action
#             llm_service = LangGraphService(base_url=settings.LANGGRAPH_BASE_URL)
#             llm_response = llm_service.get_response(
#                 question        = value_name,
#                 chat_history    = chat_history,
#                 postback_event  = {
#                     "message_type" : "postback", #message_type.lower(),
#                     "data" : postback_data,
#                 },
#                 user_profile    = user_profile,
#                 ticket_llm_endpoint = active_ticket.llm_endpoint,
#                 thread_id       = customer.customer_id,
#                 ticket_id       = active_ticket.id
#             )

#             logger.info(f"process_line_postback_v2 - llm_response: {llm_response}")

#             message_template = llm_response['message_template']
#             logger.info(f"process_line_postback_v2 - message_template: {message_template}")

#             ticket_llm_endpoint = llm_response.get('status', None)
#             company_code        = llm_response.get('channel')
#             agent               = llm_response.get('agent')
            
#             message_type_response = llm_response.get('message_template', {}).get('message_type', {})
#             message_template_id     = llm_response.get('message_template', {}).get('id', None)

#             # LINE element
#             if not (message_type_response):
#                 text            = None
#                 quick_reply     = llm_response.get('quick_reply', [])
#                 image_map       = None
#                 image_carousel  = None
#                 carousel        = None
#                 buttons_template= None
#                 confirm_template= None

#             else:
#                 text            = message_type_response.get('text')
#                 quick_reply     = message_type_response.get('quick_reply')
#                 image_map       = message_type_response.get('image_map')
#                 image_carousel  = message_type_response.get('image_carousel')
#                 carousel        = message_type_response.get('carousel')
#                 buttons_template= message_type_response.get('buttons_template')
#                 confirm_template= message_type_response.get('confirm_template')
            
#             logger.info(f"process_line_postback_v2 - ticket_llm_endpoint: {ticket_llm_endpoint}")
#             logger.info(f"process_line_postback_v2 - message_template_id: {message_template_id}")
#             logger.info(f"process_line_postback_v2 - quick_reply: {quick_reply}")

#             recommended_products = llm_response.get('product', None)
            
#             # Get MessageTemplate
#             from setting.models import MessageTemplate
#             if message_template_id:
#                 try:
#                     message_template = MessageTemplate.objects.get(id=message_template_id)
#                 except MessageTemplate.DoesNotExist:
#                     message_template = None
#             else:
#                 message_template = None
            
#             logger.info(f"process_line_postback_v2 - message_template: {message_template}")

#             # Update ticket endpoint
#             if ticket_llm_endpoint == None:
#                 ticket_llm_endpoint = 'default'
#             else:
#                 ticket_llm_endpoint = ticket_llm_endpoint
#             active_ticket.llm_endpoint = ticket_llm_endpoint
#             active_ticket.save()

#             outgoing_message = "" if text is None else text

#             # Add ticket ID for first message
#             if is_first_agent_message(active_ticket.id):
#                 icon_ticket = "\U0001F3AB"
#                 outgoing_message = f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {active_ticket.id}\n" + outgoing_message

#             # Create outgoing message
#             outgoing_message_obj = Message.objects.create(
#                 ticket_id=active_ticket,
#                 message=outgoing_message,
                
#                 user_name=active_ticket.owner_id.name,
#                 is_self=True,
#                 # message_type=Message.MessageType.TEXT,
#                 message_type=Message.MessageType.ALTERNATIVE,
#                 status=Message.MessageStatus.SENDING,
#                 platform_identity=platform_identity, # Send to this customer's platform_identity
#                 message_template=message_template,
#                 metadata={
#                     'line_channel_id': channel_id,
#                     'provider_id': provider_id,
#                     'reply_token': event_reply_token
#                 },
#                 created_by=system_user
#             )

#             logger.info(f"process_line_postback_v2 - AFTER CREATED outgoing_message_obj's message_template_id: {message_template_id}")
#             logger.info(f"process_line_postback_v2 - AFTER CREATED outgoing_message_obj's message_template: {message_template}")
#             logger.info(f"process_line_postback_v2 - AFTER CREATED outgoing_message_obj: {outgoing_message_obj.message_template}")
            
#             # Route message with LINE elements        
#             result = PlatformRoutingService.route_message_to_customer(
#                 customer        = customer,
#                 message_content = outgoing_message,
#                 message_type    = 'ALTERNATIVE',
#                 preferred_platform_identity_id=platform_identity.id,
#                 metadata        = {
#                     'ticket_id'         : active_ticket.id,
#                     'message_id'        : outgoing_message_obj.id,
#                     'message_template_id': message_template_id, 
#                     'quick_reply'       : quick_reply,
#                     'image_map'         : image_map,
#                     'image_carousel'    : image_carousel,
#                     'carousel'          : carousel,
#                     'buttons_template'  : buttons_template,
#                     'confirm_template'  : confirm_template,
#                     'reply_token'       : event_reply_token
#                 }
#             )
            
#             if result['success']:
#                 # Update message status
#                 outgoing_message_obj.status = Message.MessageStatus.DELIVERED
#                 outgoing_message_obj.delivered_on = timezone.now()
#                 outgoing_message_obj.save()
#             else:
#                 # Update to failed
#                 outgoing_message_obj.status = Message.MessageStatus.FAILED
#                 outgoing_message_obj.save()

#             # Broadcast outgoing message to WebSocket
#             # from customer.tasks import broadcast_platform_message_update
#             # if platform_identity:
#             #     # TODO - Delete this
#             #     print(f"process_postback_message_v2's broadcast_platform_message_update - {platform_identity.id} - {outgoing_message_obj.id}")
#             #     broadcast_platform_message_update.delay(
#             #         platform_identity_id=platform_identity.id,
#             #         message_id=outgoing_message_obj.id
#             #     )

#             # if agent or (ticket_llm_endpoint == 'transfered'):
#             if agent:
#                 # Get transfer context from Vector DB
#                 vector_db_api = VectorDBService(base_url=settings.VECTORDB_API_URL)
#                 vector_db_response = vector_db_api.get_response(
#                     endpoint="tools_transfering",
#                     user_profile={
#                         'user_id': active_ticket.customer_id.customer_id,
#                         'user_name': platform_context.get_display_name(),
#                         "social_platform": "line"
#                     },
#                     previous_messages=chat_history
#                 )
                
#                 handle_agent_transfer_v2.delay(
#                     ticket_id=active_ticket.id,
#                     ticket_interface_name=active_ticket.ticket_interface.name,
#                     company_code=company_code,
#                     department_code=vector_db_response.get('department'),
#                     user_tags=vector_db_response.get('tags', []),
#                     platform_context={
#                         'platform': platform_identity.platform,
#                         'platform_identity_id': platform_identity.id,
#                         'channel_id': channel_id
#                     }
#                 )
        
#     except Exception as e:
#         logger.error(f"Error processing LINE postback: {str(e)}")
        
#         # Send error message
#         if event_reply_token:
#             try:
#                 line_configuration = LineConfigService.get_config(channel_id)
#                 reply_with_text_v2(
#                     line_configuration=line_configuration,
#                     reply_token=event_reply_token,
#                     text="Sorry, I couldn't process your request. Please try again.",
#                     user_session=None
#                 )
#             except:
#                 pass



# Update for process_line_postback_v2 in linechatbot/tasks.py
# Add this section after parsing postback data and getting active_ticket
# This 3rd version - 
@shared_task(bind=True, max_retries=3, default_retry_delay=15)
def process_line_postback_v2(
    self,
    channel_id: str,
    provider_id: str,
    line_user_id: str,
    postback_data: str,
    event_reply_token: str = None,
    display_name: str = None
):
    """
    Process LINE postback events with support for LINE elements
    """
    from ticket.utils import update_ticket_status

    pending_to_close_status = Status.objects.get(name='pending_to_close')
    closed_status = Status.objects.get(name='closed')

    try:
        logger.warning(f"Processing LINE postback (process_line_postback_v2) from {line_user_id}")
        logger.info(f"process_line_postback_v2's postback_data - {postback_data}")

        # Find or create customer with platform identity
        from connectors.services.customer_identity_service import CustomerIdentityService
        from connectors.services.platform_context import PlatformContext
        
        customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform        = 'LINE',
            platform_user_id= line_user_id,
            provider_id     = provider_id,
            channel_id      = channel_id,
            display_name    = display_name,
            platform_data   = {
                'last_postback': postback_data,
                'last_interaction_type': 'postback'
            }
        )

        # Create platform context
        platform_context = PlatformContext(platform_identity)
        if event_reply_token:
            platform_context.set_reply_token(event_reply_token)

        system_user = User.objects.get(name='System')
        
        from ticket.services.ticket_service import TicketService
        active_ticket, ticket_created = TicketService.get_or_create_active_ticket(
            platform_identity=platform_identity
        )

        # SECURITY: Validate and sanitize postback data before processing
        try:
            # Validate postback data structure
            validated_postback_data = validate_and_sanitize_postback_data(postback_data)
            logger.info(f"process_line_postback_v2 - validated postback data: {validated_postback_data}")

            # Extract sanitized values
            action_name = validated_postback_data.get('action', [None])[0] if 'action' in validated_postback_data else None
            variable_name = validated_postback_data.get('variable', [None])[0] if 'variable' in validated_postback_data else None
            value_name = validated_postback_data.get('value', [None])[0] if 'value' in validated_postback_data else None
            status_name = validated_postback_data.get('status', [None])[0] if 'status' in validated_postback_data else None

            # Sanitize the message content that will be stored
            raw_postback_message = value_name or f"[Postback: {action_name}]"
            postback_message_content = validate_and_sanitize_message_content(raw_postback_message, 'text')

        except SecurityValidationError as e:
            logger.error(f"Security validation failed for postback data: {str(e)}")
            # Use safe fallback values
            action_name = "unknown"
            variable_name = None
            value_name = None
            status_name = None
            postback_message_content = "[Postback data blocked for security reasons]"
            validated_postback_data = {}

        # Parse postback data (keeping original for backward compatibility)
        parse_action = parse_qs(postback_data)

        logger.info(f"process_line_postback_v2 - parse_action: {parse_action}")
        logger.info(f"process_line_postback_v2 - sanitized values: action={action_name}, variable={variable_name}, value={value_name}")

        # Create incoming postback message - THIS IS THE KEY ADDITION
        
        # SECURITY: Sanitize display name before creating message
        try:
            sanitized_display_name = validate_and_sanitize_message_content(
                platform_context.get_display_name(), 'text'
            )
        except SecurityValidationError:
            sanitized_display_name = f"Customer {platform_identity.customer.customer_id}"

        # Create the incoming message record
        customer_message = Message.objects.create(
            ticket_id=active_ticket,
            message=postback_message_content,  # Already sanitized above
            user_name=sanitized_display_name,
            is_self=False,  # This is from customer
            message_type=Message.MessageType.TEXT,  # Postback as TEXT type
            status=Message.MessageStatus.DELIVERED,
            platform_identity=platform_identity,
            metadata={
                'line_channel_id': channel_id,
                'provider_id': provider_id,
                'reply_token': event_reply_token,
                'postback_data': postback_data,  # Keep original for debugging
                'action': action_name,
                'variable': variable_name,
                'value': value_name,
                'status': status_name,
                'security_validated': True  # Mark as security validated
            },
            created_by=system_user
        )
        
        logger.info(f"Created incoming postback message {customer_message.id} for postback action: {action_name}")
        
        # Update platform identity last interaction
        platform_identity.last_interaction = timezone.now()
        platform_identity.save(update_fields=['last_interaction'])
        
        # Broadcast incoming postback message - CRITICAL FOR REAL-TIME UPDATE
        logger.info(f"About to broadcast message {customer_message.id} for platform {platform_identity.id}")
        from customer.tasks import broadcast_platform_message_update
        broadcast_platform_message_update.delay(
            platform_identity_id=platform_identity.id,
            message_id=customer_message.id
        )
        
        logger.info(f"Broadcasted incoming postback message {customer_message.id}")

        # Now continue with the rest of the postback processing...
        postback_label = ""
        
        # Special handling for CSAT rating
        if variable_name and variable_name.lower() in ["csat_rating"]:
            postback_value = parse_action.get('value', [''])[0]
            logger.info(f"Received CSAT rating {postback_value} for Ticket ID: {active_ticket.id}")
            
            # Update the incoming message to reflect CSAT
            customer_message.message = f"CSAT Score: {postback_value}"
            customer_message.save(update_fields=['message'])
            
            # Save CSAT rating
            active_ticket.feedback = {'csat': f"{postback_value}"}
            active_ticket.save()

            outgoing_message = f"""ขอบคุณที่ให้คะแนน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n\nThank you for giving us your feedback. If you would like to inquire any information, you can contact us 24 hours a day."""

            # Create outgoing message
            outgoing_message_obj = Message.objects.create(
                ticket_id=active_ticket,
                message=outgoing_message,
                user_name=active_ticket.owner_id.name,
                is_self=True,
                message_type=Message.MessageType.TEXT,  # CSAT response as TEXT
                status=Message.MessageStatus.SENDING,
                platform_identity=platform_identity,
                metadata={
                    'line_channel_id': channel_id,
                    'provider_id': provider_id,
                    'reply_token': event_reply_token
                },
                created_by=system_user
            )
            
            # Route message
            result = PlatformRoutingService.route_message_to_customer(
                customer=customer,
                message_content=outgoing_message,
                message_type='TEXT',
                preferred_platform_identity_id=platform_identity.id,
                metadata={
                    'ticket_id': active_ticket.id,
                    'message_id': outgoing_message_obj.id,
                    'reply_token': event_reply_token
                }
            )
            
            if result['success']:
                outgoing_message_obj.status = Message.MessageStatus.DELIVERED
                outgoing_message_obj.delivered_on = timezone.now()
            else:
                outgoing_message_obj.status = Message.MessageStatus.FAILED
            outgoing_message_obj.save()
            
            # Broadcast outgoing message
            broadcast_platform_message_update.delay(
                platform_identity_id=platform_identity.id,
                message_id=outgoing_message_obj.id
            )

            # Handle ticket closure for CSAT
            if active_ticket.status_id == pending_to_close_status:
                # prompt_message = f"""ขอบคุณที่ให้คะแนน หากต้องการสอบถามข้อมูลเพิ่มเติม ลูกค้าสามารถติดต่อเราได้ตลอด 24 ชั่วโมง\n\nThank you for giving us your feedback. If you would like to inquire any information, you can contact us 24 hours a day. """
                # send_message_via_route_message_to_customer.delay(
                #         ticket_id=active_ticket.id,
                #         message_content=prompt_message,
                #         message_type='TEXT',
                #         # metadata={
                #         #     # 'line': {
                #         #     #     'image_map': line_csat_score_imagemap.to_dict(),
                #         #     # }
                #         #     'image_map': line_csat_score_imagemap
                #         # },
                #         event_reply_token=event_reply_token,
                #         bool_create_outgoing_message=True
                #     )
                update_ticket_status(ticket=active_ticket, new_status=closed_status, user=active_ticket.owner_id)
            
            return {
                'status': 'success',
                'action': 'csat_processed',
                'ticket_id': active_ticket.id
            }

        else:
            message_template_status = None
            message_template_department_list_of_ids = None


            channel_field_mapping = {
                'line': 'line_channel_id',
                'facebook': 'facebook_channel_id', 
                'whatsapp': 'whatsapp_channel_id'
            }

            # Process normal postback through LLM
            logger.info("process_line_postback_v2 - calling LLM for postback")
            logger.info('process_line_postback_v2 - platform_identity.channel_name:', getattr(platform_identity, 'channel_name', None))
            logger.info('process_line_postback_v2 - platform_identity.id:', getattr(platform_identity, 'id', None))

            filter_kwargs = {'social_app': platform_identity.platform.lower()}

            if platform_identity.platform in channel_field_mapping and platform_identity.channel_name:
                channel_field = channel_field_mapping[platform_identity.platform]
                filter_kwargs[channel_field] = platform_identity.channel_name

            conversation_flow = ConversationFlow.objects.filter(**filter_kwargs).first()
            chatbot_profile = ChatbotProfile.objects.get(chatbot_id=conversation_flow.chatbot_id)
            chat_history = get_last_n_messages_formatted(platform_identity)
            user_profile = {
                "channel"           : "all",
                "name"              : platform_context.get_display_name(),
                "social_platform"   : platform_identity.platform,
                "line_channel"      : getattr(platform_identity, 'channel_name', None),
                "facebook_channel"  : None,
                "whatsapp_channel"  : None,
                # "conversation_flow" : model_to_dict(conversation_flow),
                "chatbot_profile"   : model_to_dict(chatbot_profile)
            }

            logger.info(f"process_line_postback_v2 - user_profile for LLM: {user_profile}")

            # Process the postback action
            llm_service = LangGraphService(base_url=settings.LANGGRAPH_BASE_URL)
            llm_response = llm_service.get_response(
                question            = value_name,
                chat_history        = chat_history,
                postback_event      = {
                    "message_type": "postback",
                    "data": postback_data,
                },
                user_profile        = user_profile,
                ticket_llm_endpoint = active_ticket.llm_endpoint,
                thread_id           = customer.customer_id,
                ticket_id           = active_ticket.id
            )

            logger.info(f"process_line_postback_v2 - llm_response: {llm_response}")

            # Extract response data
            ticket_llm_endpoint = llm_response.get('status', None)
            company_code = llm_response.get('channel')
            agent = llm_response.get('agent')
            
            message_template_id = llm_response.get('message_template', {}).get('id', None)
            message_type_response = llm_response.get('message_template', {}).get('message_type', {})

            # Extract LINE elements
            if message_type_response:
                text = message_type_response.get('text')
                quick_reply = message_type_response.get('quick_reply')
                image = message_type_response.get('image')
                image_map = message_type_response.get('image_map')
                image_carousel = message_type_response.get('image_carousel')
                carousel = message_type_response.get('carousel')
                buttons_template = message_type_response.get('buttons_template')
                confirm_template = message_type_response.get('confirm_template')
            else:
                text = None
                quick_reply = llm_response.get('quick_reply', [])
                image = None
                image_map = None
                image_carousel = None
                carousel = None
                buttons_template = None
                confirm_template = None

            # Get MessageTemplate if exists
            message_template = None

            from setting.models import MessageTemplate
            if message_template_id:
                try:
                    message_template = MessageTemplate.objects.get(id=message_template_id)
                    logger.info(f"process_line_postback_v2's Found message template: {message_template}")

                    message_template_status = message_template.status
                    message_template_department_list_of_ids = message_template.department

                    ticket_llm_endpoint = message_template_status
                            
                except MessageTemplate.DoesNotExist:
                    logger.warning(f"MessageTemplate with ID {message_template_id} not found")

            logger.info(f"process_line_postback_v2's message_template_id: {message_template_id}")
            logger.info(f"process_line_postback_v2's Found message template's status: {message_template_status}")
            logger.info(f"process_line_postback_v2's Found message template's department: {message_template_department_list_of_ids}")

            # Update ticket endpoint
            if ticket_llm_endpoint is None:
                ticket_llm_endpoint = 'default'
            active_ticket.llm_endpoint = ticket_llm_endpoint
            active_ticket.save()

            # Prepare outgoing message
            outgoing_message = text if text else ""
            
            # Add ticket ID for first message
            # if is_first_agent_message(active_ticket.id):
            #     icon_ticket = "\U0001F3AB"
            #     outgoing_message = f"{icon_ticket}รหัสตั๋วอ้างอิง (Ticket ID): {active_ticket.id}\n" + outgoing_message

            # Determine message type based on template
            if message_template and (image or image_map or image_carousel or carousel or buttons_template or confirm_template):
                outgoing_message_type = Message.MessageType.ALTERNATIVE
            else:
                outgoing_message_type = Message.MessageType.TEXT

            # Create outgoing message
            outgoing_message_obj = Message.objects.create(
                ticket_id           = active_ticket,
                message             = outgoing_message,
                user_name           = active_ticket.owner_id.name,
                is_self             = True,
                message_type        = outgoing_message_type,
                status              = Message.MessageStatus.SENDING,
                platform_identity   = platform_identity,
                message_template    = message_template,
                metadata={
                    'line_channel_id'       : channel_id,
                    'provider_id'           : provider_id,
                    'reply_token'           : event_reply_token,
                    'response_to_postback'  : postback_data
                },
                created_by=system_user
            )

            logger.info(f"Created outgoing message {outgoing_message_obj.id} with template: {message_template_id}")
            
            # Route message with LINE elements
            result = PlatformRoutingService.route_message_to_customer(
                customer=customer,
                message_content=outgoing_message,
                message_type='ALTERNATIVE' if message_template else 'TEXT',
                preferred_platform_identity_id=platform_identity.id,
                metadata={
                    'ticket_id'             : active_ticket.id,
                    'message_id'            : outgoing_message_obj.id,
                    'message_template_id'   : message_template_id,
                    'text'                  : text,
                    'quick_reply'           : quick_reply,
                    'image'                 : image,
                    'image_map'             : image_map,
                    'image_carousel'        : image_carousel,
                    'carousel'              : carousel,
                    'buttons_template'      : buttons_template,
                    'confirm_template'      : confirm_template,
                    'reply_token'           : event_reply_token
                }
            )
            
            if result['success']:
                outgoing_message_obj.status = Message.MessageStatus.DELIVERED
                outgoing_message_obj.delivered_on = timezone.now()
            else:
                outgoing_message_obj.status = Message.MessageStatus.FAILED
                outgoing_message_obj.error_detail = result.get('error', 'Delivery failed')
            outgoing_message_obj.save()

            # Broadcast outgoing message
            broadcast_platform_message_update.delay(
                platform_identity_id    = platform_identity.id,
                message_id              = outgoing_message_obj.id
            )
            
            logger.info(f"Broadcasted outgoing message {outgoing_message_obj.id}")

            # # Handle agent transfer if needed
            # if agent:
            #     vector_db_api = VectorDBService(base_url=settings.VECTORDB_API_URL)
            #     vector_db_response = vector_db_api.get_response(
            #         endpoint="tools_transfering",
            #         user_profile={
            #             'user_id': active_ticket.customer_id.customer_id,
            #             'user_name': platform_context.get_display_name(),
            #             "social_platform": "line"
            #         },
            #         previous_messages=chat_history
            #     )
                
            #     handle_agent_transfer_v2.delay(
            #         ticket_id=active_ticket.id,
            #         ticket_interface_name=active_ticket.ticket_interface.name,
            #         company_code=company_code,
            #         department_code=vector_db_response.get('department'),
            #         user_tags=vector_db_response.get('tags', []),
            #         platform_context={
            #             'platform': platform_identity.platform,
            #             'platform_identity_id': platform_identity.id,
            #             'channel_id': channel_id
            #         }
            #     )

            # Check if ticket owner is System (chatbot should respond)
            owner_role = UserRole.objects.filter(user_id=active_ticket.owner_id).first()
            if message_template_status == "close" and owner_role.role_id.name == "System" and active_ticket.status_id.name == "open":
                update_ticket_status(ticket=active_ticket, new_status=closed_status, user=active_ticket.owner_id)


            # Handle agent transfer if needed
            elif agent or message_template_status == "transferred" and owner_role.role_id.name == "System":
                transfer_to_department_codes=None
                transfer_to_user_tags=[]
                # Extract prefer user's department if provided
                if message_template_department_list_of_ids:
                        departments = Department.objects.filter(id__in=message_template_department_list_of_ids)
                        # TODO - If automate transfering can selecta suitable user from one of department codes then this section of codes have to be updated
                        transfer_to_department_codes = list(departments.values_list('code', flat=True))[0]
                else:
                    vector_db_api = VectorDBService(base_url=settings.VECTORDB_API_URL)
                    vector_db_response = vector_db_api.get_response(
                        endpoint="tools_transfering",
                        user_profile={
                            'user_id': active_ticket.customer_id.customer_id,
                            'user_name': platform_context.get_display_name(),
                            "social_platform": "line"
                        },
                        previous_messages=chat_history
                    )
                    transfer_to_department_codes=vector_db_response.get('department', None)
                    transfer_to_user_tags=vector_db_response.get('tags', [])

                logger.info(f"process_line_postback_v2's transfer_to_department_codes - {transfer_to_department_codes}")
                logger.info(f"process_line_postback_v2's transfer_to_user_tags - {transfer_to_user_tags}")
                    
                handle_agent_transfer_v2.delay(
                    ticket_id=active_ticket.id,
                    ticket_interface_name=active_ticket.ticket_interface.name,
                    # company_code=company_code,
                    department_code=transfer_to_department_codes,
                    user_tags=transfer_to_user_tags,
                    platform_context={
                        'platform': platform_identity.platform,
                        'platform_identity_id': platform_identity.id,
                        'channel_id': channel_id
                    }
                )
            
            return {
                'status': 'success',
                'action': 'postback_processed',
                'ticket_id': active_ticket.id,
                'incoming_message_id': customer_message.id,
                'outgoing_message_id': outgoing_message_obj.id
            }
        
    except Exception as e:
        logger.error(f"Error processing LINE postback: {str(e)}", exc_info=True)
        
        # Send error message if possible
        if 'platform_identity' in locals() and event_reply_token:
            try:
                error_msg = "ขออภัย เกิดข้อผิดพลาดในระบบ\n(System error occurred. Please try again.)"
                error_msg = "ขออภัย เกิดข้อผิดพลาดในระบบ"
                error_message = Message.objects.create(
                    ticket_id=active_ticket,
                    message=error_msg,
                    user_name="System",
                    is_self=True,
                    message_type=Message.MessageType.TEXT,
                    status=Message.MessageStatus.SENDING,
                    platform_identity=platform_identity,
                    created_by=system_user
                )
                
                PlatformRoutingService.route_message_to_customer(
                    customer=platform_identity.customer,
                    message_content=error_msg,
                    message_type='TEXT',
                    preferred_platform_identity_id=platform_identity.id,
                    metadata={
                        'is_error': True,
                        'reply_token': event_reply_token
                    }
                )
                
                broadcast_platform_message_update.delay(
                    platform_identity_id=platform_identity.id,
                    message_id=error_message.id
                )
            except:
                pass
        
        return {
            'status': 'error',
            'error': str(e)
        }


@shared_task(bind=True, max_retries=3, default_retry_delay=15)
def send_line_response_v2(self, ine_user_id, message, ticket_id, message_id):
    """
    Send LINE response with platform routing support.
    """
    try:
        from ticket.models import Ticket, Message
        from connectors.services.platform_routing_service import PlatformRoutingService
        
        ticket = Ticket.objects.get(id=ticket_id)
        db_message = Message.objects.get(id=message_id)
        
        # Route message through platform routing service
        result = PlatformRoutingService.route_message_to_customer(
            customer=ticket.customer_id,
            message_content=message,
            message_type='TEXT',
            metadata={
                'ticket_id': ticket_id,
                'message_id': message_id
            }
        )
        
        if result['success']:
            # Update message status
            db_message.status = Message.MessageStatus.DELIVERED
            db_message.delivered_on = timezone.now()
            db_message.save()
            
            # Broadcast status update
            from ticket.tasks import broadcast_to_websocket
            broadcast_to_websocket.delay(
                ticket_id=ticket_id,
                message_id=message_id,
                action='message_status_update'
            )
        else:
            # Update message status to failed
            db_message.status = Message.MessageStatus.FAILED
            db_message.save()
            
            logger.error(f"Failed to send LINE message: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error in send_line_response: {str(e)}")
        return {'success': False, 'error': str(e)}

# @shared_task(bind=True, max_retries=3)
@shared_task(bind=True, max_retries=3, default_retry_delay=15)
def handle_agent_transfer_v2(
    self, 
    ticket_id, 
    ticket_interface_name, 
    company_code=None, 
    department_code=None, 
    user_tags=None,
    platform_context=None
):
    """
    Handle the transfer of a ticket to a human agent with platform-aware notifications.
    """
    try:
        logger.info(f"Starting agent transfer for ticket {ticket_id}")

        CHATBOT_MASCOT_THAI_NAME = SettingsService.get_setting("CHATBOT_MASCOT_THAI_NAME")
        CHATBOT_MASCOT_ENGLISH_NAME = SettingsService.get_setting("CHATBOT_MASCOT_ENGLISH_NAME")
        
        COUNT_SENDING_MESSAGE_ABOUT_OUTSIDE_BUSINESS_HOUR = 0

        # Get ticket and key objects
        ticket = Ticket.objects.get(id=ticket_id)
        ticket_owner = ticket.owner_id
        customer = ticket.customer_id
        
        # Get platform identity if provided
        platform_identity = None
        if platform_context and platform_context.get('platform_identity_id'):
            try:
                from customer.models import CustomerPlatformIdentity
                platform_identity = CustomerPlatformIdentity.objects.get(
                    id=platform_context['platform_identity_id']
                )
            except CustomerPlatformIdentity.DoesNotExist:
                logger.warning(f"Platform identity {platform_context['platform_identity_id']} not found")
        
        if not SchedulingService.is_within_business_hours() and COUNT_SENDING_MESSAGE_ABOUT_OUTSIDE_BUSINESS_HOUR < 1:
            # Send a message to notify a customer that a ticket is transferred during outside business hour

            # # Both Thai and English message about outside business hour
            # # Define the sentences
            # english_notice = "It is currently outside of our business hours.\n\n"
            # thai_notice = "ขออภัย ขณะนี้อยู่นอกเวลาทำการ\n\n"
            
            # status = SchedulingService.get_company_status(languages=['th', 'en'])
            # prompt_message = status['prompt_message']

            # parts = prompt_message.split("---")
            # prompt_message = thai_notice + parts[0].strip() + "\n\n---\n\n" + english_notice + parts[1].strip()

            # Extract the message
            prompt_message = SettingsService.get_setting("COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS")

            send_message_via_route_message_to_customer.delay(
                    ticket_id=ticket.id,
                    message_content=prompt_message,
                    message_type='TEXT',
                    # metadata={
                    #     # 'line': {
                    #     #     'image_map': line_csat_score_imagemap.to_dict(),
                    #     # }
                    #     'image_map': line_csat_score_imagemap
                    # },
                    event_reply_token=None,
                    bool_create_outgoing_message=True
                )
            
            COUNT_SENDING_MESSAGE_ABOUT_OUTSIDE_BUSINESS_HOUR += 1

        # Find a suitable user
        suitable_user = UserStatusService.get_suitable_user(
            ticket_interface_name=ticket_interface_name,
            company_code=company_code,
            department_code=department_code,
            user_tags=user_tags
        )
        
        if suitable_user['user']: # If there is no suitable user then its value is None
            logger.info(f"Found suitable user {suitable_user} for ticket {ticket_id}")
            
            # Get assigned status
            assigned_status = Status.objects.get(name="assigned")
            
            # Transfer ticket and update status
            from ticket.utils import transfer_ticket_owner_v2, update_ticket_status
            transfer_ticket_owner_v2(ticket, suitable_user['user'], ticket_owner)
            update_ticket_status(ticket, assigned_status, ticket_owner)
            
            # Get latest summary
            latest_analysis = TicketAnalysis.objects.filter(
                ticket_id=ticket_id
            ).order_by('-created_on').first()
            
            summary_message = "No summary available"
            if latest_analysis:
                summary_message = latest_analysis.summary
            
            # # Notify the assigned user
            # from user._services.staff_notification_service import StaffNotificationService
            # notification_result = StaffNotificationService.notify_ticket_transfer(
            #     ticket=ticket,
            #     from_user=ticket_owner,
            #     to_user=suitable_user,
            #     reason=f"Ticket assigned with summary: {summary_message[:100]}..."
            # )

            # TODO - Check with the sending message in transfer_ticket_owner_v2
            # # Notify customer about assignment
            # if platform_identity:
            #     customer_message = (
            #         # f"\U0001F916 น้อง{CHATBOT_MASCOT_THAI_NAME} :\n"
            #         f"\U0001F3AB รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n"
            #         f"ทางเราได้ส่งเรื่องให้เจ้าหน้าที่รับทราบแล้ว "
            #         f"ทางเจ้าหน้าที่จะติดต่อผ่านทางช่องทางนี้"
            #     )
                
            #     from connectors.services.platform_routing_service import PlatformRoutingService
            #     PlatformRoutingService.route_message_to_customer(
            #         customer=customer,
            #         message_content=customer_message,
            #         message_type='TEXT',
            #         preferred_platform_identity_id=platform_identity.id
            #     )
            
            return {
                'status': 'success',
                'transfer_type': 'assigned',
                'assigned_to': suitable_user['user'].id,
                # 'notification_sent': notification_result.get('success', False)
            }
            
        else:
            logger.info(f"No suitable user found for ticket {ticket_id}, setting to waiting")
            
            # No suitable agent available
            waiting_status = Status.objects.get(name="waiting")
            from ticket.utils import update_ticket_status
            update_ticket_status(ticket, waiting_status, ticket_owner)
            
            # Get latest summary for notification
            from devproject.utils.utils import remove_bracketed_text
            from django.utils.timezone import localtime
            
            latest_analysis = TicketAnalysis.objects.filter(
                ticket_id=ticket_id
            ).order_by('-created_on').first()
            
            if latest_analysis:
                # summary = remove_bracketed_text(latest_analysis.summary)
                # Get Thai Sumamry first if it has a value
                summary = latest_analysis.summary.get('thai')  or latest_analysis.summary.get('english')
                sentiment = latest_analysis.sentiment
                
                # # TODO - Update this section of codes when doing notification on Website
                # # Notify supervisors
                # from user._services.staff_notification_service import StaffNotificationService
                # supervisor_result = StaffNotificationService.notify_waiting_ticket_to_supervisors(
                #     ticket=ticket,
                #     summary=summary,
                #     sentiment=sentiment,
                #     notify_all=True  # Notify all supervisors for waiting tickets
                # )
                
                # logger.info(f"Supervisor notification result: {supervisor_result}")
            
            # Notify customer about waiting status while inside a company's business hour
            if platform_identity and COUNT_SENDING_MESSAGE_ABOUT_OUTSIDE_BUSINESS_HOUR == 0:
                customer_message = (
                    # f"\U0001F916 {CHATBOT_MASCOT_THAI_NAME} :\n"
                    # f"\U0001F3AB รหัสตั๋วอ้างอิง (Ticket ID): {ticket.id}\n"
                    f"ขณะนี้เจ้าหน้าที่ทุกคนกำลังให้บริการลูกค้าท่านอื่นอยู่ "
                    f"ทางเราจะติดต่อกลับโดยเร็วที่สุด"
                )
                
                from connectors.services.platform_routing_service import PlatformRoutingService
                PlatformRoutingService.route_message_to_customer(
                    customer=customer,
                    message_content=customer_message,
                    message_type='TEXT',
                    preferred_platform_identity_id=platform_identity.id
                )
            
            return {
                'status': 'success',
                'transfer_type': 'waiting',
                # 'supervisors_notified': supervisor_result.get('notified_count', 0)
            }
    
    except Exception as e:
        logger.error(f"Error in handle_agent_transfer_v2: {str(e)}")
        raise self.retry(exc=e, countdown=10)
    






# # This version is Before CSAT score as MessageTemplate
# @shared_task(bind=True, max_retries=3)
# def send_message_via_route_message_to_customer(
#     self,
#     ticket_id,
#     message_content,
#     message_type='TEXT',
#     metadata={},
#     event_reply_token=None
#     # ticket_id: int, 
#     # message_content: str, 
#     # # preferred_platform_identity_id,
#     # message_type: str='TEXT', 
#     # metadata: Optional[dict] = None,
#     # event_reply_token = None
# ):
#     """
#     Send a message to a customer using the platform routing service.
#     Example of metadata:
#     {
#         'ticket_id': 123,
#         'message_id': 456,
#         'quick_reply': None,
#         'image_map': None,
#         'image_carousel': ImagemapMessage().to_dict(),
#         'carousel': None,
#         'recommended_products': None,
#         'reply_token': 'abc123'
#     }
#     """
#     try:

#         logger.info(f"Starting sending a message to a customer via routing platform")

#         # TODO - Delete this or Log this
#         print(f"send_message_via_route_message_to_customer's ticket_id - {ticket_id}")

#         # Get required objects
#         ticket = Ticket.objects.get(id=ticket_id)
#         system_user = User.objects.get(name='System')

#         # # Get messages with platform identity for this ticket
#         # message_with_platform = Message.objects.filter(
#         #     ticket_id=ticket,
#         #     platform_identity__isnull=False
#         # ).first()

#         # Get the latest customer message with platform identity
#         message_with_platform = Message.objects.filter(
#             ticket_id=ticket,
#             is_self=False,  # Only consider customer messages
#             platform_identity__isnull=False
#         ).order_by('-created_on').first()

#         if message_with_platform and message_with_platform.platform_identity:
#             platform_identity = message_with_platform.platform_identity
            
#             platform = platform_identity.platform
#             platform_user_id = platform_identity.platform_user_id
#             provider_id = platform_identity.provider_id
#             channel_id = platform_identity.channel_id
#             display_name = platform_identity.display_name
#             platform_data = platform_identity.platform_data

#         customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
#             platform='LINE',
#             platform_user_id=platform_user_id,
#             provider_id=provider_id,
#             channel_id=channel_id,
#             display_name=display_name,
#             platform_data={
#                 'last_message': message_content,
#                 'last_message_type': message_type
#             }
#         )

#         # Create platform context
#         platform_context = PlatformContext(platform_identity)
#         if event_reply_token:
#             platform_context.set_reply_token(event_reply_token)

#         # Update platform identity last interaction
#             platform_identity.last_interaction = timezone.now()
#             platform_identity.save(update_fields=['last_interaction'])
        
#         # Create outgoing message
#         outgoing_message_obj = Message.objects.create(
#             ticket_id=ticket,
#             message=message_content, # Text or Alternvative text of objects such as image map, carousel, etc.
#             user_name=ticket.owner_id.name,
#             is_self=True,
#             message_type=Message.MessageType.TEXT,
#             status=Message.MessageStatus.SENDING,
#             platform_identity=platform_identity, # Send to this customer's platform_identity
#             metadata={
#                 'line_channel_id': channel_id,
#                 'provider_id': provider_id,
#                 'reply_token': event_reply_token
#             },
#             created_by=system_user
#         )

#         # Update metadata
#         metadata['ticket_id'] = ticket.id
#         metadata['message_id'] = outgoing_message_obj.id
#         if 'reply_token' not in metadata:
#             metadata['reply_token'] = event_reply_token if platform_context.should_use_reply() else None

#         # Send response through platform routing
#         result = PlatformRoutingService.route_message_to_customer(
#             customer=customer,
#             message_content=message_content,
#             message_type=message_type,
#             preferred_platform_identity_id=platform_identity.id,
#             metadata=metadata
#         )

#         return result
    
#     except Exception as e:
#         logger.error(f"Error in send_message_via_route_message_to_customer: {str(e)}")
#         raise self.retry(exc=e, countdown=10)




@shared_task(bind=True, max_retries=3, default_retry_delay=15)
def send_message_via_route_message_to_customer(
    self,
    ticket_id,
    message_content,
    message_type='TEXT',
    metadata={},
    event_reply_token=None,
    bool_create_outgoing_message=False,
    # ticket_id: int, 
    # message_content: str, 
    # # preferred_platform_identity_id,
    # message_type: str='TEXT', 
    # metadata: Optional[dict] = None,
    # event_reply_token = None
):
    """
    Send a message to a customer using the platform routing service.
    Example of metadata:
    {
        'ticket_id': 123,
        'message_id': 456,
        'quick_reply': None,
        'image_map': None,
        'image_carousel': ImagemapMessage().to_dict(),
        'carousel': None,
        'recommended_products': None,
        'reply_token': 'abc123'
    }
    """
    try:

        logger.info(f"Starting sending a message to a customer via routing platform")

        # TODO - Delete this or Log this
        print(f"send_message_via_route_message_to_customer's ticket_id - {ticket_id}")

        # Get required objects
        ticket = Ticket.objects.get(id=ticket_id)
        system_user = User.objects.get(name='System')

        if ticket and ticket.platform_identity:
            platform_identity = ticket.platform_identity
            
            platform = platform_identity.platform
            platform_user_id = platform_identity.platform_user_id
            provider_id = platform_identity.provider_id
            channel_id = platform_identity.channel_id
            display_name = platform_identity.display_name
            platform_data = platform_identity.platform_data

        # # Get messages with platform identity for this ticket
        # message_with_platform = Message.objects.filter(
        #     ticket_id=ticket,
        #     platform_identity__isnull=False
        # ).first()

        # # Get the latest customer message with platform identity
        # message_with_platform = Message.objects.filter(
        #     ticket_id=ticket,
        #     is_self=False,  # Only consider customer messages
        #     platform_identity__isnull=False
        # ).order_by('-created_on').first()

        # if message_with_platform:
        #     # Get latest message from User-side instead
        #     message_with_platform = Message.objects.filter(
        #         ticket_id=ticket,
        #         is_self=True,
        #         platform_identity__isnull=False
        #     ).order_by('-created_on').first()

        # if message_with_platform and message_with_platform.platform_identity:
        #     platform_identity = message_with_platform.platform_identity
            
        #     platform = platform_identity.platform
        #     platform_user_id = platform_identity.platform_user_id
        #     provider_id = platform_identity.provider_id
        #     channel_id = platform_identity.channel_id
        #     display_name = platform_identity.display_name
        #     platform_data = platform_identity.platform_data

        customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
            platform='LINE',
            platform_user_id=platform_user_id,
            provider_id=provider_id,
            channel_id=channel_id,
            display_name=display_name,
            platform_data={
                'last_message': message_content,
                'last_message_type': message_type
            }
        )

        # Create platform context
        platform_context = PlatformContext(platform_identity)
        if event_reply_token:
            platform_context.set_reply_token(event_reply_token)

        # Update platform identity last interaction
            platform_identity.last_interaction = timezone.now()
            platform_identity.save(update_fields=['last_interaction'])
        
        # If message_template_id is provided, fetch the template
        message_template = None
        message_template_id = metadata.get('message_template_id', None)
        if message_template_id:
            from setting.models import MessageTemplate
            try:
                message_template = MessageTemplate.objects.get(id=message_template_id)
                message_content = message_template.label  # Use template content as message

                metadata['message_template_id'] = message_template_id
                logger.warning(f"MessageTemplate with ID {message_template_id} not found, using default content")

            except MessageTemplate.DoesNotExist:
                logger.warning(f"MessageTemplate with ID {message_template_id} not found, using default content")

        # Create outgoing message
        if bool_create_outgoing_message:
            if message_type.upper() == 'IMAGE':
                outgoing_message_type = Message.MessageType.IMAGE
            elif message_type.upper() == 'FILE':
                outgoing_message_type = Message.MessageType.FILE
            elif message_type.upper() == 'ALTERNATIVE':
                outgoing_message_type = Message.MessageType.ALTERNATIVE
            else:
                outgoing_message_type = Message.MessageType.TEXT
            
            outgoing_message_obj = Message.objects.create(
                ticket_id=ticket,
                message=message_content, # Text or Alternvative text of objects such as image map, carousel, etc.
                user_name=ticket.owner_id.name,
                is_self=True,
                message_type=outgoing_message_type,
                status=Message.MessageStatus.SENDING,
                platform_identity=platform_identity, # Send to this customer's platform_identity
                metadata={
                    'line_channel_id': channel_id,
                    'provider_id': provider_id,
                    'reply_token': event_reply_token
                },
                message_template=message_template,
                created_by=system_user
            )
        else:
            outgoing_message_obj = Message.objects.filter(
                ticket_id=ticket,
                is_self=True
                # status='DELIVERED'
            ).order_by('-created_on').first()

        # Update metadata
        metadata['ticket_id'] = ticket.id
        metadata['message_id'] = outgoing_message_obj.id
        if 'reply_token' not in metadata:
            metadata['reply_token'] = event_reply_token if platform_context.should_use_reply() else None

        # Send response through platform routing
        result = PlatformRoutingService.route_message_to_customer(
            customer=customer,
            message_content=message_content,
            message_type=message_type,
            preferred_platform_identity_id=platform_identity.id,
            metadata=metadata,
        )

        from customer.tasks import broadcast_platform_message_update
        broadcast_platform_message_update.delay(
            platform_identity_id=platform_identity.id,
            message_id=outgoing_message_obj.id
        )

        return result
    
    except Exception as e:
        logger.error(f"Error in send_message_via_route_message_to_customer: {str(e)}")
        raise self.retry(exc=e, countdown=10)









































# This version is for Customer-Centric page (v2)
from connectors.message_processor import message_processor
from connectors.base import MessageFormat
from customer.tasks import broadcast_typing_indicator, broadcast_message_status_update


def process_line_message_v2_updated(
    channel_id: str,
    provider_id: Optional[str],
    line_user_id: str,
    message_content: str,
    message_type: str,
    event_reply_token: str,
    display_name: Optional[str] = None
):
    """
    Updated LINE message processor with customer-centric broadcasting.
    """
    try:
        # Create standardized message format
        message = MessageFormat(
            channel_type='line',
            channel_id=channel_id,
            message_id=f"line_{timezone.now().timestamp()}",
            sender_id=line_user_id,
            recipient_id='business',
            message_type=message_type.lower(),
            content={'text': message_content} if message_type == 'TEXT' else {},
            timestamp=int(timezone.now().timestamp()),
            metadata={
                'reply_token': event_reply_token,
                'provider_id': provider_id,
                'channel_id': channel_id,
                'display_name': display_name,
                'source_type': 'user'
            }
        )
        
        # Process message through enhanced processor
        result = message_processor.process_message(message)
        
        if result['success']:
            logger.info(f"Successfully processed LINE message: {result}")
            
            # Continue with LINE-specific processing (chatbot response, etc.)
            # ... existing LINE processing code ...
        
    except Exception as e:
        logger.error(f"Error processing LINE message: {str(e)}")


def send_line_response_v2_updated(
    line_user_id: str,
    message: str,
    ticket_id: int,
    message_id: int
):
    """
    Updated LINE response sender with status broadcasting.
    """
    try:
        # Get message and platform identity
        from ticket.models import Message
        msg = Message.objects.select_related('platform_identity').get(id=message_id)
        
        # Update status to sending
        msg.status = Message.MessageStatus.SENDING
        msg.save(update_fields=['status'])
        
        # Broadcast status update
        if msg.platform_identity:
            broadcast_message_status_update.delay(message_id, 'SENDING')
        
        # Send through LINE
        # ... existing LINE sending code ...
        
        # Update status to delivered
        msg.status = Message.MessageStatus.DELIVERED
        msg.delivered_on = timezone.now()
        msg.save(update_fields=['status', 'delivered_on'])
        
        # Broadcast delivery status
        if msg.platform_identity:
            broadcast_message_status_update.delay(message_id, 'DELIVERED')
        
    except Exception as e:
        logger.error(f"Error sending LINE response: {str(e)}")
        
        # Update status to failed
        if 'msg' in locals():
            msg.status = Message.MessageStatus.FAILED
            msg.save(update_fields=['status'])
            
            if msg.platform_identity:
                broadcast_message_status_update.delay(message_id, 'FAILED')

@shared_task
def process_incomplete_image_sets():
    """
    Periodic task to process incomplete image sets that have timed out.
    Runs every minute to check for sets older than IMAGE_BUFFER_TIMEOUT_MINUTES.
    """
    try:
        from connectors.line.image_buffer import LineImageBuffer
        from connectors.line.line_config_service import LineConfigService
        from connectors.services.customer_identity_service import CustomerIdentityService
        from customer.models import Customer
        from devproject.utils.azure_storage import AzureBlobStorage
        
        logger.info("Starting processing of incomplete image sets")
        
        # Initialize image buffer
        image_buffer = LineImageBuffer()
        
        # Get incomplete sets older than timeout
        timeout_minutes = getattr(settings, 'IMAGE_BUFFER_TIMEOUT_MINUTES', 3)
        incomplete_sets = image_buffer.get_incomplete_sets(older_than_minutes=timeout_minutes)
        
        logger.info(f"Found {len(incomplete_sets)} incomplete image sets to process")
        
        for set_info in incomplete_sets:
            try:
                set_id = set_info['set_id']
                set_data = set_info['data']
                
                if not set_data or not set_data.get('images'):
                    logger.warning(f"No images found in incomplete set {set_id}")
                    image_buffer.clear_set(set_id)
                    continue
                
                # Get the first image to extract user info
                first_image = set_data['images'][0]
                line_user_id = first_image.get('line_user_id')
                channel_id = first_image.get('channel_id')
                provider_id = first_image.get('provider_id')
                display_name = first_image.get('display_name')
                
                if not all([line_user_id, channel_id]):
                    logger.error(f"Missing required data for set {set_id}")
                    image_buffer.clear_set(set_id)
                    continue
                
                logger.info(f"Processing incomplete set {set_id} with {len(set_data['images'])}/{set_data['total']} images")
                
                # # Get customer
                # customer_identity = CustomerIdentityService.get_or_create_identity(
                #     platform='LINE',
                #     platform_user_id=line_user_id,
                #     provider_id=provider_id,
                #     display_name=display_name
                # )
                # customer = customer_identity.customer

                # Get customer
                customer, platform_identity, created = CustomerIdentityService.find_or_create_customer_from_platform(
                    platform='LINE',
                    platform_user_id=line_user_id,
                    provider_id=provider_id,
                    channel_id=channel_id,
                    display_name=display_name,
                )
                
                # Initialize LINE connector for image downloading
                from connectors.line.connector import LineConnector
                line_connector = LineConnector(channel_id)
                
                # Process available images
                file_urls = []
                azure_storage = AzureBlobStorage()
                
                for img_data in set_data['images']:
                    try:
                        message_id = img_data.get('message_id')
                        index = img_data.get('index', 1)
                        
                        # Download image from LINE
                        logger.info(f"Downloading incomplete set image {index} (ID: {message_id})")
                        image_data = line_connector.download_line_image(message_id)
                        
                        # Generate filename
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"{timestamp}_{line_user_id}_{message_id}_{index}_incomplete.jpeg"
                        
                        # Add name attribute for upload method
                        image_data.name = filename
                        
                        # Upload to Azure
                        blob_url = customer.upload_file(image_data, filename)
                        
                        # Get URL with SAS token
                        blob_name = f"{customer.blob_folder}{filename}"
                        image_url_with_sas = azure_storage.get_file_url_image_v2(
                            blob_name, 
                            sas_lifetime_days=365
                        )
                        
                        file_urls.append(image_url_with_sas)
                        
                    except Exception as e:
                        logger.error(f"Error processing image {index} in incomplete set {set_id}: {str(e)}")
                
                # Create message content
                received = len(file_urls)
                total = set_data['total']
                message_content = (f"[Received {received}/{total} images - {total - received} failed] / "
                                 f"[ได้รับ {received}/{total} รูป - อีก {total - received} รูปอัพโหลดไม่สำเร็จ]")
                
                # Use the last available reply token (might be expired)
                reply_token = None
                for img in reversed(set_data['images']):
                    if img.get('reply_token'):
                        reply_token = img['reply_token']
                        break
                
                # Process the message
                process_line_message_v2.delay(
                    channel_id=channel_id,
                    provider_id=provider_id,
                    line_user_id=line_user_id,
                    message_content=message_content,
                    message_type=Message.MessageType.IMAGE,
                    event_reply_token=reply_token or '',
                    display_name=display_name,
                    file_urls=file_urls
                )
                
                # Clear the set from buffer
                image_buffer.clear_set(set_id)
                
                logger.info(f"Successfully processed incomplete set {set_id} with {received}/{total} images")
                
            except Exception as e:
                logger.error(f"Error processing incomplete set {set_info['set_id']}: {str(e)}")
                # Clear the problematic set
                try:
                    image_buffer.clear_set(set_info['set_id'])
                except:
                    pass
        
        logger.info("Completed processing of incomplete image sets")
        return f"Processed {len(incomplete_sets)} incomplete image sets"
        
    except Exception as e:
        logger.error(f"Error in process_incomplete_image_sets task: {str(e)}")
        raise