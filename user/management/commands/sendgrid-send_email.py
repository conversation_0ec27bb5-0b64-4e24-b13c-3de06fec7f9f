import os
import base64
from django.core.management.base import BaseCommand

from sendgrid import Send<PERSON>ridAPIClient
from sendgrid.helpers.mail import (
    Mail, 
    Attachment, 
    FileContent, 
    FileName,
    FileType, 
    Disposition, 
    ContentId
)

class Command(BaseCommand):
    help = "Sending Email with SendGrid APIs"

    SENDGRID_API_KEY = os.environ.get("SENDGRID_API_KEY")
    SENDGRID_FROM_EMAIL = os.environ.get("SENDGRID_FROM_EMAIL")
    SENDGRID_TO_MAIL = os.environ.get("SENDGRID_TO_MAIL")

    def handle(self, *args, **kwargs):
        try:
            self._simple_send_email()
            self._send_email_with_attachment()
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during initialization: {str(e)}'))
            raise
    
    def _simple_send_email(self):
        """
        Send Email to a receiptent with SendGrid APIs
        """

        message = Mail(
            from_email=self.SENDGRID_FROM_EMAIL,
            to_emails=self.SENDGRID_TO_MAIL,
            subject="Testing _simple_send_email function from Salmate Development Environment",
            html_content='<strong>This is my simple HTML body</strong>'
        )

        sg = SendGridAPIClient(api_key=self.SENDGRID_API_KEY)
        response = sg.send(message)
        print(response.status_code, response.body)

    def _send_email_with_attachment(self):
        """
        Send Email to a receiptent with attachment(s) with SendGrid APIs
        """

        file_path = 'media/test_pdf1.pdf'
        with open(file_path, 'rb') as f:
            data = f.read()
            f.close()
        encoded = base64.b64encode(data).decode()
        attachment = Attachment()
        attachment.file_content = FileContent(encoded)
        attachment.file_type = FileType('application/pdf')
        attachment.file_name = FileName('test_filename.pdf')
        attachment.disposition = Disposition('attachment')
        attachment.content_id = ContentId('Example Content ID')

        message = Mail(
            from_email=self.SENDGRID_FROM_EMAIL,
            to_emails=self.SENDGRID_TO_MAIL,
            subject="Testing sending email from Salmate Development Environment",
            html_content='<strong>This is my simple HTML body</strong>'
        )

        message.attachment = attachment

        sg = SendGridAPIClient(api_key=self.SENDGRID_API_KEY)
        response = sg.send(message)
        print(response.status_code, response.body, response.headers)

