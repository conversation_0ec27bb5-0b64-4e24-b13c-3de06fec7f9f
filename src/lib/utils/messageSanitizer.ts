import { t } from "../stores/i18n";

/**
 * Result type for message validation and sanitization
 */
export interface MessageValidationResult {
    sanitizedContent: string;
    isValid: boolean;
    error: string;
}

/**
 * Unified function that validates and sanitizes message input for submission
 * Performs comprehensive security checks, content sanitization, and validation
 * @param input - The message content to validate and sanitize
 * @param skipSanitization - If true, skips sanitization for rich text mode (default: false)
 */
export function validateAndSanitizeMessage(input: string, skipSanitization: boolean = false): MessageValidationResult {
    // Early validation check for empty input
    if (!input || input.trim() === '') {
        return {
            sanitizedContent: '',
            isValid: true,
            error: ''
        };
    }

    // If skipSanitization is true (rich text mode), return input with minimal validation
    if (skipSanitization) {
        // For rich text mode, only check for the most dangerous patterns
        const criticalPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript\s*:/gi,
            /vbscript\s*:/gi,
            /data\s*:/gi,
            /alert\s*\([^)]*\)/gi
        ];

        for (const pattern of criticalPatterns) {
            if (pattern.test(input)) {
                return {
                    sanitizedContent: input,
                    isValid: false,
                    error: t('input_error_message_contains_invalid_content')
                };
            }
        }

        return {
            sanitizedContent: input,
            isValid: true,
            error: ''
        };
    }

    // Perform comprehensive sanitization for plain text mode
    let sanitized = input;

    // Remove script tags and their content (preserve whitespace outside of tags)
    sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');

    // Remove dangerous HTML tags while preserving surrounding whitespace
    const dangerousTags = [
        'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
        'link', 'meta', 'style', 'base', 'applet', 'frame', 'frameset',
        'div', 'span', 'img', 'a', 'p', 'br', 'hr', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'table', 'thead', 'tbody', 'tfoot', 'tr', 'th', 'td', 'pre',
        'code', 'blockquote', 'address', 'ins', 'del', 's', 'u', 'b', 'i', 'em', 'strong',
        'mark', 'small', 'big', 'sup', 'sub', 'tt', 'var', 'cite', 'dfn', 'abbr', 'acronym',
        'bdo', 'q', 'samp', 'kbd', 'param', 'map', 'area', 'noframes',
        'select', 'option', 'optgroup', 'textarea', 'label', 'fieldset',
        'legend', 'datalist', 'keygen', 'output', 'progress', 'meter', 'details', 'summary',
        'command', 'menu', 'canvas', 'audio', 'video', 'source', 'track', 'noscript'
    ];

    dangerousTags.forEach(tag => {
        const regex = new RegExp(`<\\/?${tag}\\b[^>]*>`, 'gi');
        sanitized = sanitized.replace(regex, '');
    });

    // Remove javascript:, data: protocols, and alert() calls
    sanitized = sanitized.replace(/javascript\s*:/gi, '');
    sanitized = sanitized.replace(/data\s*:/gi, '');
    sanitized = sanitized.replace(/alert\s*\([^)]*\)/gi, '');

    // Remove on* event handlers with more precise matching
    // This targets actual HTML attributes, not normal text containing "on"
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*["'][^"']*["']/gi, '');
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*[^\s>]+/gi, '');

    // Only limit consecutive angle brackets that could be malicious HTML
    // Preserve normal greater/less than symbols in text
    sanitized = sanitized.replace(/[<>]{3,}/g, '<<>>');
    sanitized = sanitized.replace(/[{}]{3,}/g, '{{}}');

    // Remove any remaining HTML-like patterns that could be dangerous
    // But be more selective to avoid removing legitimate content
    sanitized = sanitized.replace(/<\s*\/?\s*[a-zA-Z][^>]*>/gi, '');

    // Only trim leading/trailing whitespace, preserve internal whitespace
    sanitized = sanitized.replace(/^\s+|\s+$/g, '');

    // Final validation check for any remaining suspicious patterns after sanitization
    const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /vbscript:/i,
        /onload=/i,
        /onerror=/i,
        /onclick=/i
    ];

    for (const pattern of suspiciousPatterns) {
        if (pattern.test(sanitized) || sanitized.trim() === '') {
            return {
                sanitizedContent: sanitized,
                isValid: false,
                error: t('input_error_message_contains_invalid_content')
            };
        }
    }

    return {
        sanitizedContent: sanitized,
        isValid: true,
        error: ''
    };
}
